# https://kubernetes.io/docs/concepts/services-networking/ingress/#the-ingress-resource

apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: test-gm-fashion-ingress
  namespace: ccc
  annotations:
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /l36/fashion/gm/$1
spec:
  ingressClassName: nginx
  tls:
    - hosts:
        - "preview.swordofjustice.com"
      secretName: swordofjustice-tls-preview
  rules:
    - host: preview.swordofjustice.com
      http:
        paths:
          - path: /l36/gm/fashion/(.*)
            pathType: Prefix
            backend:
              service:
                name: l36-fashion-api-server-test
                port:
                  number: 80