# https://kubernetes.io/docs/concepts/services-networking/ingress/#the-ingress-resource

apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: test-ingress
  namespace: ccc
  annotations:
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/configuration-snippet: |
      location ~* /(swagger) {
        return 403;
      }
      location ~* /(api-docs) {
        return 403;
      }    
spec:
  ingressClassName: nginx
  tls:
    - hosts:
        - "preview.swordofjustice.com"
      secretName: swordofjustice-tls-preview
  rules:
    - host: preview.swordofjustice.com
      http:
        paths:
          - path: /l36/gardenStation/
            pathType: Prefix
            backend:
              service:
                name: l36-garden-api-server-test
                port:
                  number: 80
          - path: /l36/fashion/
            pathType: Prefix
            backend:
              service:
                name: l36-fashion-api-server-test
                port:
                  number: 80
          - path: /l36/
            pathType: Prefix
            backend:
              service:
                name: l36-md-api-client-server-preview
                port:
                  number: 80