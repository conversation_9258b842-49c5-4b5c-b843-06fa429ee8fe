# https://kubernetes.io/docs/concepts/services-networking/ingress/#the-ingress-resource

apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: gm-garden-ingress
  namespace: ccc
  annotations:
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /l36/garden/gm/$1
spec:
  ingressClassName: nginx
  tls:
    - hosts:
        - "api.swordofjustice.com"
      secretName: swordofjustice-tls
  rules:
    - host: api.swordofjustice.com
      http:
        paths:
          - path: /l36/gm/garden/(.*)
            pathType: Prefix
            backend:
              service:
                name: l36-garden-api-client-server-release
                port:
                  number: 80