# https://kubernetes.io/docs/concepts/services-networking/ingress/#the-ingress-resource

apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ngp-ingress
  namespace: ccc
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /$2
    nginx.ingress.kubernetes.io/use-regex: "true"
spec:
  ingressClassName: nginx
  tls:
    - hosts:
        - "api.swordofjustice.com"
      secretName: swordofjustice-tls
  rules:
    - host: api.swordofjustice.com
      http:
        paths:
          - path: /ngp(/|$)(.*)
            pathType: Prefix
            backend:
              service:
                name: ngp-nshm-service
                port:
                  number: 80
