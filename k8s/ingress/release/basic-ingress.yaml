# https://kubernetes.io/docs/concepts/services-networking/ingress/#the-ingress-resource

apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: basic-ingress
  namespace: ccc
  annotations:
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/configuration-snippet: |
      location ~* /(swagger) {
        return 403;
      }
      location ~* /(api-docs) {
        return 403;
      }       
spec:
  ingressClassName: nginx
  tls:
    - hosts:
        - "api.swordofjustice.com"
      secretName: swordofjustice-tls
  rules:
    - host: api.swordofjustice.com
      http:
        paths:
          - path: /l36/fashion/(sync|auth|server)
            pathType: ImplementationSpecific
            backend:
              service:
                name: l36-fashion-api-server-server-release
                port:
                  number: 80
          - path: /l36/fashion/
            pathType: Prefix
            backend:
              service:
                name: l36-fashion-api-client-server-release
                port:
                  number: 80
          - path: /l36/gardenStation/(sync|auth|server)
            pathType: ImplementationSpecific
            backend:
              service:
                name: l36-garden-api-server-server-release
                port:
                  number: 80
          - path: /l36/gardenStation/
            pathType: Prefix
            backend:
              service:
                name: l36-garden-api-client-server-release
                port:
                  number: 80
          - path: /l36/(sync|auth|server)
            pathType: ImplementationSpecific
            backend:
              service:
                name: l36-md-api-server-server-release
                port:
                  number: 80
          - path: /danmu/
            pathType: Prefix
            backend:
              service:
                name: minio-nginx
                port:
                  number: 80
          - path: /l36/
            pathType: Prefix
            backend:
              service:
                name: l36-md-api-client-server-release
                port:
                  number: 80
