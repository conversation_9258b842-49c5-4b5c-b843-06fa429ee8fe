apiVersion: v1
kind: Service
metadata:
  name: l36-md-api-client-server-test
  namespace: ccc
spec:
  selector:
    app: l36-md-api-client-server-test
    group: node
    func: md
  type: NodePort
  sessionAffinity: None
  ports:
    - name: http
      protocol: TCP
      port: 80
      targetPort: 4001
      # If you set the `spec.type` field to `NodePort` and you want a specific port number,
      # you can specify a value in the `spec.ports[*].nodePort` field.
      nodePort: 30001