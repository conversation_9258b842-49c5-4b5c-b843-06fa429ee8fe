apiVersion: apps/v1
kind: Deployment
metadata:
  name: l36-md-api-client-server-test
  namespace: ccc
  labels:
    app: l36-md-api-client-server-test
spec:
  replicas: 1
  selector:
    matchLabels:
      app: l36-md-api-client-server-test
      group: node
      func: md
  template:
    metadata:
      labels:
        app: l36-md-api-client-server-test
        group: node
        func: md
    spec:
      imagePullSecrets:
          - name: regcred
      volumes:
        - name: config-volume
          configMap:
            name: l36-md-api-test
            items:
              - key: config.js
                path: config.js
        - name: client-pm2-volume
          configMap:
            name: l36-md-api-test
            items:
              - key: client-pm2.json
                path: pm2.json
        - name: yunying-log
          hostPath:
            path: /srv/logs/pyq
            type: DirectoryOrCreate
      containers:
        - name: l36-md-api-server-test
          image: l36na-reg.leihuo.netease.com/ccc/l36-api:na_develop.359065.496bbf94
          ports:
            - name: http
              containerPort: 4001
              protocol: TCP
          resources:
            requests:
              memory: "500Mi"
              cpu: "100m"
            limits:
              memory: "500Mi"
              cpu: "200m"
          volumeMounts:
            - name: config-volume
              mountPath: /data/dist/config/config.js
              subPath: config.js
            - name: client-pm2-volume
              mountPath: /data/bin/pm2.json
              subPath: pm2.json
            - name: yunying-log
              mountPath: /yunying