# https://kubernetes.io/docs/concepts/services-networking/ingress/#the-ingress-resource

apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: test-jp-ingress
  namespace: l36sea
  annotations:
    nginx.ingress.kubernetes.io/use-regex: "true"
spec:
  ingressClassName: nginx
  tls:
    - hosts:
        - "preview-jp.swordofjustice.com"
      secretName: swordofjustice-tls-preview
  rules:
    - host: preview-jp.swordofjustice.com
      http:
        paths:
          - path: /l36/
            pathType: Prefix
            backend:
              service:
                name: l36-md-api-client-server-jp-preview
                port:
                  number: 80
          - path: /l36/gardenStation/
            pathType: Prefix
            backend:
              service:
                name: l36-garden-api-server-jp-test
                port:
                  number: 80
          - path: /l36/fashion/
            pathType: Prefix
            backend:
              service:
                name: l36-fashion-api-server-jp-test
                port:
                  number: 80