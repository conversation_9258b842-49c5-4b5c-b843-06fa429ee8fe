# https://kubernetes.io/docs/concepts/services-networking/ingress/#the-ingress-resource

apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: gm-garden-jp-ingress
  namespace: l36sea
  annotations:
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /l36/garden/gm/$1
spec:
  ingressClassName: nginx
  tls:
    - hosts:
        - "api-jp.swordofjustice.com"
      secretName: swordofjustice-tls
  rules:
    - host: api-jp.swordofjustice.com
      http:
        paths:
          - path: /l36/gm/garden/(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: l36-garden-api-client-server-jp-release
                port:
                  number: 80