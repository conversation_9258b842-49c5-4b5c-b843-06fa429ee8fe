apiVersion: apps/v1
kind: Deployment
metadata:
  name: l36-md-api-consumer-release
  namespace: ccc
  labels:
    app: l36-md-api-consumer-release
spec:
  replicas: 2
  selector:
    matchLabels:
      app: l36-md-api-consumer-release
      group: node
      func: md
  template:
    metadata:
      labels:
        app: l36-md-api-consumer-release
        group: node
        func: md
    spec:
      volumes:
        - name: config-volume
          configMap:
            name: l36-md-api
            items:
              - key: config.js
                path: config.js
        - name: consumer-pm2-volume
          configMap:
            name: l36-md-api
            items:
              - key: kafka-sync-pm2.json
                path: pm2.json
      containers:
        - name: l36-md-consumer-release
          image: l36hmt-reg.leihuo.netease.com/ccc/l36-api:hmt_develop.248578.5b6f712d
          ports:
            - name: http
              containerPort: 4001
              protocol: TCP
          resources:
            requests:
              memory: "2Gi"
              cpu: "1"
            limits:
              memory: "2Gi"
              cpu: "1"
          volumeMounts:
            - name: config-volume
              mountPath: /data/dist/config/config.js
              subPath: config.js
            - name: consumer-pm2-volume
              mountPath: /data/bin/pm2.json
              subPath: pm2.json
