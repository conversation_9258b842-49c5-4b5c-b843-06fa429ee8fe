apiVersion: apps/v1
kind: Deployment
metadata:
  name: l36-danmu-nginx
  namespace: ccc
  labels:
    app: l36-danmu-nginx
spec:
  replicas: 1
  selector:
    matchLabels:
      app: l36-danmu-nginx
      group: node
      func: md
  template:
    metadata:
      labels:
        app: l36-danmu-nginx
        group: node
        func: md
    spec:
      volumes:
        - name: config-volume
          configMap:
            name: l36-danmu-nginx-config
            items:
              - key: rsyncd.conf
                path: rsyncd.conf
              - key: rsyncd.secrets
                path: rsyncd.secrets
              - key: default.conf
                path: default.conf
        # - name: danmu-nginx-volume
        #   persistentVolumeClaim:
        #     claimName:
        #       danmu-nginx-pvc
      containers:
        - name: l36-danmu-nginx
          image: l36hmt-reg.leihuo.netease.com/ccc/nginx-rsync:main.146207.5257b17e
          ports:
            - name: http
              containerPort: 80
              protocol: TCP
          readinessProbe:
            httpGet:
              path: /healthCheck
              port: 80
            initialDelaySeconds: 3
            periodSeconds: 10
            timeoutSeconds: 1
            successThreshold: 1
            failureThreshold: 3
          resources:
            requests:
              memory: "500Mi"
              cpu: "200m"
            limits:
              memory: "500Mi"
              cpu: "200m"
          volumeMounts:
            - name: config-volume
              mountPath: /srv/rsync/rsyncd.conf
              subPath: rsyncd.conf
            - name: config-volume
              mountPath: /srv/rsync/rsyncd.secrets
              subPath: rsyncd.secrets
            - name: config-volume
              mountPath: /srv/nginx/sites-enabled/default.conf
              subPath: default.conf
            # - name: danmu-nginx-volume
            #   mountPath: /srv/logs/error/
            #   subPath: error
            # - name: danmu-nginx-volume
            #   mountPath: /srv/sites/danmu/
            #   subPath: danmu
            # - name: danmu-nginx-volume
            #   mountPath: /srv/sites/garden/
            #   subPath: garden
