apiVersion: v1
kind: ConfigMap
metadata:
  name: l36-danmu-nginx-config
  namespace: ccc
data:
  rsyncd.conf: |
    address = 0.0.0.0
    fake super = yes
    list = no
    max connections = 1024
    port = 873
    reverse lookup = no
    secrets file = /etc/rsyncd.secrets
    syslog facility = daemon
    use chroot = no

    [danmu]
    uid = root
    gid = www-data
    path = /srv/sites/danmu
    read only = no
    auth users = root
    incoming chmod = Du=rwx,Dg=rx,Do=,Fu=rw,Fg=r,Fo=

    [garden]
    uid = root
    gid = www-data
    path = /srv/sites/garden
    read only = no
    auth users = root
    incoming chmod = Du=rwx,Dg=rx,Do=,Fu=rw,Fg=r,Fo=

  rsyncd.secrets: |
    root:2IsXCBvz9Po0pmsr

  default.conf: |
    server {
        listen 80;
        server_name localhost;

        access_log /srv/logs/default.access.log json;
        error_log /srv/logs/error/default.error.log;

        location / {
            root /srv/sites/danmu;
            index  index.html index.htm;
        }

        location /danmu/ {
            root /srv/sites;
            index  index.html index.htm;
        }

        location /garden/ {
            root /srv/sites;
            index  index.html index.htm;
        }

        location /healthCheck {
          add_header Content-Type text/plain;
          return 200 'ok';
        }
    }
