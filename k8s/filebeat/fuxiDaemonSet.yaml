apiVersion: v1
kind: ConfigMap
metadata:
  name: filebeat-config-to-fuxi
  namespace: ccc
  labels:
    k8s-app: filebeat
data:
  filebeat.yml: |-
    filebeat.inputs:
    - type: filestream
      paths:
        - /srv/logs/**/*.log

    output.kafka:
      hosts: ["kafka-gcp.inner.fuxi.netease.com:9092"]
      topic: 'l36hmt_media_operation'
      username: 'ccc_l36hmt_pyq_wr'
      password: 'Fc4bD5m2GbDf4dLZ'
      compression: gzip
      required_acks: 1
      max_message_bytes: 10000000
      partition.round_robin:
        reachable_only: false
      codec.format:
        string: '%{[message]}'
---
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: filebeat-pyq-fuxi
  namespace: ccc
  labels:
    k8s-app: filebeat
spec:
  selector:
    matchLabels:
      k8s-app: filebeat
  template:
    metadata:
      labels:
        k8s-app: filebeat
    spec:
      containers:
        - name: filebeat-pyq-fuxi
          image: l36hmt-reg.leihuo.netease.com/ccc/elastic/filebeat:8.13.4
          args: ["-c", "/etc/filebeat.yml", "-e"]
          securityContext:
            runAsUser: 0
            # If using Red Hat OpenShift uncomment this:
            #privileged: true
          resources:
            limits:
              cpu: 100m
              memory: 200Mi
            requests:
              cpu: 100m
              memory: 100Mi
          volumeMounts:
            - name: config
              mountPath: /etc/filebeat.yml
              readOnly: true
              subPath: filebeat.yml
            - name: srvlog
              mountPath: /srv/logs
              readOnly: true
      volumes:
        - name: config
          configMap:
            defaultMode: 0640
            name: filebeat-config-to-fuxi
        - name: srvlog
          hostPath:
            path: /srv/logs
