apiVersion: v1
kind: ConfigMap
metadata:
  name: filebeat-config-community
  namespace: l36sea
  labels:
    k8s-app: filebeat
data:
  filebeat.yml: |-
    filebeat.inputs:
    - type: filestream
      paths:
        - /srv/community/**/*.log
        - /srv/data/logs/app/**/*.log

    # 添加 registry 配置，避免重启后重复推送
    filebeat.registry.path: /var/lib/filebeat/registry
    filebeat.registry.flush: 1s

    output.kafka:
      hosts: ["tokyo04.brokers.canal.netease.com:9093"]
      topic: 'ccc_app_abroad_release'
      username: 'other_dep295_ccc_dc'
      password: 'd8fda63d3e954ce0b8ea'
      compression: gzip
      required_acks: 1
      max_message_bytes: 10000000
      partition.round_robin:
        reachable_only: false
      codec.format:
        string: '%{[message]}'
---
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: filebeat-community
  namespace: l36sea
  labels:
    k8s-app: filebeat
spec:
  selector:
    matchLabels:
      k8s-app: filebeat
  template:
    metadata:
      labels:
        k8s-app: filebeat
    spec:
      containers:
        - name: filebeat-community
          image: l36na-reg.leihuo.netease.com/ccc/elastic/filebeat:8.13.4
          args: ["-c", "/etc/filebeat.yml", "-e"]
          securityContext:
            runAsUser: 0
            # If using Red Hat OpenShift uncomment this:
            #privileged: true
          resources:
            limits:
              cpu: 100m
              memory: 200Mi
            requests:
              cpu: 100m
              memory: 100Mi
          volumeMounts:
            - name: config
              mountPath: /etc/filebeat.yml
              readOnly: true
              subPath: filebeat.yml
            - name: srvcommunity
              mountPath: /srv/community
              readOnly: true
            - name: filebeat-data
              mountPath: /var/lib/filebeat
            - name: srvlog
              mountPath: /srv/data/logs/app
              readOnly: true
      volumes:
        - name: config
          configMap:
            defaultMode: 0640
            name: filebeat-config-community
        - name: srvcommunity
          hostPath:
            path: /srv/community
        - name: srvlog
          hostPath:
            path: /srv/data/logs/app
            type: DirectoryOrCreate
        - name: filebeat-data
          hostPath:
            path: /var/lib/filebeat
            type: DirectoryOrCreate        
