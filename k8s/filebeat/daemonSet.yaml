apiVersion: v1
kind: ConfigMap
metadata:
  name: filebeat-yunying-config
  namespace: l36sea
  labels:
    k8s-app: filebeat
data:
  filebeat.yml: |-
    filebeat.inputs:
    - type: filestream
      paths:
        - /srv/logs/**/*.log
      exclude_files:
        - /srv/logs/app/**/*.log

    # 添加 registry 配置，避免重启后重复推送
    filebeat.registry.path: /var/lib/filebeat/yunying_registry
    filebeat.registry.flush: 1s

    output.kafka:
      hosts: ["tokyo02.brokers.canal.netease.com:9093"]
      topic: 'l36hmt_media_operation'
      username: 'other_l36hmt_media_operation'
      password: '509633181ea84a8bae3f'
      compression: gzip
      required_acks: 1
      max_message_bytes: 10000000
      partition.round_robin:
        reachable_only: false
      codec.format:
        string: '%{[message]}'
---
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: filebeat-yunying
  namespace: l36sea
  labels:
    k8s-app: filebeat
spec:
  selector:
    matchLabels:
      k8s-app: filebeat
  template:
    metadata:
      labels:
        k8s-app: filebeat
    spec:
      containers:
        - name: filebeat
          image: l36na-reg.leihuo.netease.com/ccc/elastic/filebeat:8.13.4
          args: ["-c", "/etc/filebeat.yml", "-e"]
          securityContext:
            runAsUser: 0
            # If using Red Hat OpenShift uncomment this:
            #privileged: true
          resources:
            limits:
              cpu: 200m
              memory: 200Mi
            requests:
              cpu: 100m
              memory: 100Mi
          volumeMounts:
            - name: config
              mountPath: /etc/filebeat.yml
              readOnly: true
              subPath: filebeat.yml
            - name: srvlog
              mountPath: /srv/logs
              readOnly: true
            - name: filebeat-data
              mountPath: /var/lib/filebeat
      volumes:
        - name: config
          configMap:
            defaultMode: 0640
            name: filebeat-yunying-config
        - name: srvlog
          hostPath:
            path: /srv/logs
        - name: filebeat-data
          hostPath:
            path: /var/lib/filebeat
            type: DirectoryOrCreate