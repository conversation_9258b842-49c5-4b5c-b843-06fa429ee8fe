# 自建/海外k8s集群如何推送日志

本文介绍怎么把日志采集到kafka,分为三步.
1. deployment日志挂载到物理机目录
2. 物理机目录日志推送到kafka,该步骤一般会有sa支持,如果没有,那我们就要自己搞
3. [可选]数源创建日志接入,支持kibana搜索

# deployment日志挂载到物理机目录
这一步基本上已经做到了自动化,看一下我们通用框架是怎么做的
```
...
      containers:
      ...
        volumeMounts:
        - mountPath: /srv/logs/app
          name: logdir
        - mountPath: /srv/logs/bury
          name: burydir
        ...
      volumes:
      - hostPath:
          path: /srv/data/logs/app/test
          type: DirectoryOrCreate
        name: logdir
      - hostPath:
          path: /srv/data/logs/bury/test
          type: DirectoryOrCreate
        name: burydir
      ...
```
关键配置是这些,分为业务日志/srv/logs/app和埋点日志/srv/logs/bury.
分别挂载物理机目录/srv/data/logs/app/test; /srv/data/logs/bury/test

以上就是我们自动生成的yaml关于日志挂载方面的内容,可以覆盖绝大多数应用场景.如果自己跑一个定时任务,消费进程啥的服务,或者需要推送运营日志,也这样抄一下就行.

# 日志推送

## 有sa支持
有sa支持,我们只要描述一下需求就行了
比如: 物理机的/srv/data/logs/app/test需要推送kafka,集群是xxx,topic是xxx
然后坐等结果

## 无sa支持
没有支持,我这里介绍用filebeat来进行推送日志.

首先部署的类型需要使用`DaemonSet`,它确保全部（或者某些）节点上运行一个`Pod`的副本。当有节点加入集群时，也会为他们新增一个`Pod`。当有节点从集群移除时，这些`Pod`也会被回收。删除`DaemonSet`将会删除它创建的所有`Pod`。

### Filebeat关键配置点

#### 1. 部署架构
- **DaemonSet**: 确保每个节点都运行一个filebeat实例，实现全集群日志采集
- **ConfigMap**: 存储filebeat配置文件，便于管理和更新
- **hostPath挂载**: 直接挂载物理机日志目录，避免容器内路径问题

#### 2. 输入配置 (filebeat.inputs)
- **type: filestream**: 使用文件流模式，支持实时监控文件变化
- **paths**: 支持通配符路径和多目录配置，如`/srv/data/logs/app/**/*.log`
- **exclude_files**: 排除不需要的文件，避免采集临时文件

#### 3. 注册表配置 (registry)
- **filebeat.registry.path**: 存储文件读取位置，避免重启后重复推送
- **filebeat.registry.flush**: 定期刷新注册表，确保数据持久化

#### 5. 资源限制
- **CPU**: 限制100m，避免影响节点性能
- **Memory**: 限制200Mi，防止内存溢出

#### 6. 安全配置
- **runAsUser: 0**: 以root用户运行，确保有足够权限读取日志文件
- **readOnly挂载**: 日志目录只读挂载，提高安全性

然后我这里直接给一个推送的例子:
```
apiVersion: v1
kind: ConfigMap
metadata:
  name: filebeat-config-community
  namespace: xxxx
  labels:
    k8s-app: filebeat
data:
  filebeat.yml: |-
    filebeat.inputs:
    - type: filestream
      paths:
        - /srv/community/**/*.log
        - /srv/data/logs/app/**/*.log
      exclude_files:
        - /srv/logs/app/tmp.log    

    # 添加 registry 配置，避免重启后重复推送
    filebeat.registry.path: /var/lib/filebeat/${filebeat_name}
    filebeat.registry.flush: 1s

    output.kafka:
      hosts: ["${host}"]
      topic: '${topic}'
      username: '${username}'
      password: '${password}'
      compression: gzip
      required_acks: 1
      max_message_bytes: 10000000
      partition.round_robin:
        reachable_only: false
      codec.format:
        string: '%{[message]}'
---
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: filebeat-community
  namespace: xxxx
  labels:
    k8s-app: filebeat
spec:
  selector:
    matchLabels:
      k8s-app: filebeat
  template:
    metadata:
      labels:
        k8s-app: filebeat
    spec:
      containers:
        - name: filebeat-community
          image: l36na-reg.leihuo.netease.com/ccc/elastic/filebeat:8.13.4
          args: ["-c", "/etc/filebeat.yml", "-e"]
          securityContext:
            runAsUser: 0
            # If using Red Hat OpenShift uncomment this:
            #privileged: true
          resources:
            limits:
              cpu: 100m
              memory: 200Mi
            requests:
              cpu: 100m
              memory: 100Mi
          volumeMounts:
            - name: config
              mountPath: /etc/filebeat.yml
              readOnly: true
              subPath: filebeat.yml
            - name: srvcommunity
              mountPath: /srv/community
              readOnly: true
            - name: filebeat-data
              mountPath: /var/lib/filebeat
            - name: srvlog
              mountPath: /srv/data/logs/app
              readOnly: true
      volumes:
        - name: config
          configMap:
            defaultMode: 0640
            name: filebeat-config-community
        - name: srvcommunity
          hostPath:
            path: /srv/community
        - name: srvlog
          hostPath:
            path: /srv/data/logs/app
            type: DirectoryOrCreate
        - name: filebeat-data
          hostPath:
            path: /var/lib/filebeat
            type: DirectoryOrCreate        

```
这个是我经过几个版本总结的一个filebeat配置.

### 配置说明

#### 需要替换的变量
- `${host}`: Kafka集群地址
- `${topic}`: 目标topic名称  
- `${username}`: Kafka用户名
- `${password}`: Kafka密码
- `${filebeat_name}`: filebeat实例名称，用于区分不同实例的注册表
- 镜像我已经推送丹炉: hub.fuxi.netease.com/leihuo-web-go/elastic/filebeat:8.13.4,有需要可以拉取使用

#### 部署步骤
1. 替换配置中的变量值
2. 修改namespace为实际使用的命名空间
3. 根据实际日志路径调整paths配置和containers的挂载路径
4. 应用配置：`kubectl apply -f filebeat.yaml`




# 数源创建日志接入,支持kibana搜索
对于业务日志我们需要接入kibana搜索.

## 国内流程
https://shuyuan.fuxi.netease.com/workflow/home  
这里选择应用日志接入,不会看数源的用户手册就行了.

## 海外流程
海外有点特殊,需要一些人工流程,我这里记录一下.

* 首先需要一个海外的topic用于接收日志.
我们目前已经有两个海外业务日志的通用topic
ccc_app_abroad_test、ccc_app_abroad_release
如果需要acl密码,请联系徐志荣

* 然后去群号：1479140寻求帮助.请他们申请这个gdc topic读取权限.然后配置在数源平台.
伏羲测让我们在接入日志时选择leihuo_ccc_common,并且使用国内的数源地址.
其中ccc_app_abroad_release这个topic我已经请他们接入过.如果是新topic.需要重新走这一步.
* 剩下和国内流程一致
