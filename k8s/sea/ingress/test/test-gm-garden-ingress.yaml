# https://kubernetes.io/docs/concepts/services-networking/ingress/#the-ingress-resource

apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: test-gm-garden-sea-ingress
  namespace: l36sea
  annotations:
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /l36/garden/gm/$1
spec:
  ingressClassName: nginx
  tls:
    - hosts:
        - "preview-sea.swordofjustice.com"
      secretName: swordofjustice-tls-preview
  rules:
    - host: preview-sea.swordofjustice.com
      http:
        paths:
          - path: /l36/gm/garden/(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: l36-garden-api-server-sea-test
                port:
                  number: 80