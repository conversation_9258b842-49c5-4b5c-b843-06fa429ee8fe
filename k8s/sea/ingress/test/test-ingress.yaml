# https://kubernetes.io/docs/concepts/services-networking/ingress/#the-ingress-resource

apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: test-sea-ingress
  namespace: l36sea
  annotations:
    nginx.ingress.kubernetes.io/use-regex: "true"
spec:
  ingressClassName: nginx
  tls:
    - hosts:
        - "preview-sea.swordofjustice.com"
      secretName: swordofjustice-tls-preview
  rules:
    - host: preview-sea.swordofjustice.com
      http:
        paths:
          - path: /l36/
            pathType: Prefix
            backend:
              service:
                name: l36-md-api-client-server-sea-preview
                port:
                  number: 80
          - path: /l36/gardenStation/
            pathType: Prefix
            backend:
              service:
                name: l36-garden-api-server-sea-test
                port:
                  number: 80
          - path: /l36/fashion/
            pathType: Prefix
            backend:
              service:
                name: l36-fashion-api-server-sea-test
                port:
                  number: 80