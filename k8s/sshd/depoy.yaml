apiVersion: apps/v1
kind: Deployment
metadata:
  name: l36-sshd-server
  namespace: l36sea
  labels:
    app: l36-sshd-server
spec:
  replicas: 1
  selector:
    matchLabels:
      app: l36-sshd-server
      group: node
      func: sshd
  template:
    metadata:
      labels:
        app: l36-sshd-server
        group: node
        func: sshd
    spec:
      imagePullSecrets:
        - name: regcred
      volumes:
        - name: config-volume
          configMap:
            name: l36-sshd-server
            items:
              - key: authorized_keys
                path: authorized_keys
      containers:
        - name: l36-sshd-server
          image: l36na-reg.leihuo.netease.com/ccc/sshd:latest
          ports:
            - name: http
              containerPort: 22
              protocol: TCP
          resources:
            requests:
              memory: "100Mi"
              cpu: "100m"
            limits:
              memory: "100Mi"
              cpu: "100m"
          volumeMounts:
            - name: config-volume
              mountPath: /home/<USER>/.ssh/authorized_keys
              subPath: authorized_keys
          env:
            - name: SSH_USERS
              value: www:48:48,admin:1000:1000:/bin/bash
            - name: TCP_FORWARDING
              value: 'true'