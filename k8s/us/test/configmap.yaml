apiVersion: v1
kind: ConfigMap
metadata:
  name: l36-md-api-us-preview
  namespace: l36sea
data:
  config.js: |
    "use strict";
    const dConfig = require("../common/config.all");
    let config = Object.assign({}, dConfig);
    config.testCfg = {
      req_log: true,
      sync_log: true,
      test_env: true,
      debug: false,
      qa_debug: false,
      db_debug: true,
      redis_debug: false,
      request_debug: false,
      show_doc: false,
      skip_openId_auth: false,
      skip_ip_check: false,
      skip_level_check: true, //等级限制没了
      skip_skey_check: false,
    };

    config.db = {
      connectionLimit: 20,
      port: 3306,
      host: "rm-gs5p1rb1x9769740p.mysql.singapore.rds.aliyuncs.com",
      user: "nshm_us",
      password: "ccwfbsdycgyu",
      database: "l36_us_preview",
      charset: "utf8mb4",
    };

    config.log = {
      dir: "/srv/logs",
      yunyingdir: "/yunying",
      level: "debug",
      prefix: "l36_api",
      printInConsole: false,
      env: "l36_us_preview",
    };
    config.slaveDb = config.db; //TODO modify

    config.commonCommentCfg.addCommentCD = 1;
    config.JWT_TOKEN_SECRET = "testForUS"

    config.redis = {
      hosts: ["r-gs5e6d8b28cac374.redis.singapore.rds.aliyuncs.com"],
      host: "r-gs5e6d8b28cac374.redis.singapore.rds.aliyuncs.com",
      port: 6379,
      db: 0,
      password: "61DWNqUc6tRM5GLa",
      no_ready_check: false,
      keyPrefix: "l36_us_preview:",
      enableAutoPipelining: true,
    };

    if (config.momentLotteryRedis) {
      config.momentLotteryRedis = config.redis;
    }

    config.Features.openKafkaApi = true;
    config.Features.momentVisit = true;
    config.Features.momentLottery = true;

    config.openIdCfg.mode = 'complete'
    config.openIdCfg.clientId = '922b279c379211efb7900242ac120002';
    config.openIdCfg.clientSecret = 'cae6fc70a97142ea88da4505983fc75c922b2ae4379211efb7900242ac120002';
    config.openIdCfg.cookieCfg = {
        path: '/',
        domain: '.swordofjustice.com',
        httpOnly: true,
        sameSite: 'none',
        secure: true
    }
    config.openIdCfg.redirectUri = "https://preview-sea.swordofjustice.com/l36/npcAdmin/openIdCheck"

    config.FpCfg.devMode = false;
    config.FpCfg.project = "l36na-pyq"
    config.FpCfg.region = "EaseBar"
    config.FpCfg.secretKey = "ZfamTM1ZOkrs8zmvCjPckqN4R89YdGlS";
    config.FpCfg.callbackUrl = "http://l36-garden-api-server-server-sea-release/l36/server/fpFileReviewCallbackWithoutDispatch";

    config.chatbotCfg.baseUrl = "https://linksrv-l36-pyq-hmt-test.apps-hp.danlu.netease.com/api/v1/dialogue";
    config.chatbotCfg.agent_id = "AG-905780754032635904";
    config.chatbotCfg.robot_id = "RT-905780754124910592";

    config.TopicCfg.listCacheSeconds = 60;

    config.mdCfg.openMinLevel = 0;

    config.removeRoleCommentDelAction = "phy";

    config.serverUrlCfg = "https://l36hmt.update.easebar.com/serverlist/serverlist.txt"

    config.alarmPaoPao3Cfg.url = "http://file.mg.163.com/redmine/popomsgccc.php";

    config.dbSlowLogCfg.threshold = 5000;
    config.MIN_SERVER_ID_IN_PROD = 0


    config.fatalAlarmCfg.ignoreRegArr = [
      /update[\s\S]+ set[\s\S]+where `ID` =/,
      /insert into `l36_roleInfo`[\s\S]+ on duplicate key update/,
      /insert into `l36_profile`[\s\S]+ on duplicate key update/,
      /l36_moment_like.idx_moment_role/,
      /ER_DUP_ENTRY/,
    ];

    config.chatbotQueueCfg.batchSize = 1;

    if (config.danmuCfg) {
      config.danmuCfg.danmuFileDir = "/yunying/danmu";
      config.danmuCfg.rsyncFileGlob = "/yunying/danmu/danmu*.json";
      config.danmuCfg.rsyncPassword = "2IsXCBvz9Po0pmsr";
      config.danmuCfg.rsyncRemoteUrl = "root@l36-danmu-nginx:873/danmu/";
      config.danmuCfg.fakeCdnPath =  true;
    }

    if (config.kafka) {
      config.kafka.brokers = ["pulsar://aliyun01.brokers.pulsar.netease.com:6650"];
      config.kafka.topic = "persistent://game/game/l36na_pulsar";
      config.kafka.username = "";
      config.kafka.password = "";
      config.kafka.logReg = /\[(.*?)\]\[(.*?)\],(\{.*\})/;
      config.kafka.justConsumeServers = [830,816,832,811,812,813,814,815,817,818,819,820,821,822,823,824,825,826,827,828,829]
      config.kafka.openOnlineServerLimit = false;
      config.kafka.useClient = 'pulsar';
    }


    config.ipNetCfg.token = "e1d1206616f111eeb2f1525400aa5830";
    config.ipNetCfg.url = "http://ip-info-service/api/getIpInfo";
    config.envSdkUrl = "http://l36-envsdk-server-release:80";

    config.gameServerIpWhiteList = [
      //TODO modify
      "************",
      "**************",
      "**************",
      "**************",
      "*************",
      "**************",
      "*************",
      "**************",
      "**************",
    ];

    if (config.friendModelCfg) {
      config.friendModelCfg.enable = true;
    }

    if (config.CronTaskSwitch) {
      config.CronTaskSwitch.AuditDanmuByFriendModel = true;
    }

    if (config.commonCommentAdminCfg && config.commonCommentAdminCfg.chatGptCfg) {
      //TODO modify
      config.commonCommentAdminCfg.chatGptCfg.npc.agentId =
        "AG-l36-homepage-comment-prod";
      config.commonCommentAdminCfg.chatGptCfg.npc.url =
        "https://linksrv-homepage-pro-11238-80.apps-cae.danlu.netease.com/text_response";
      config.commonCommentAdminCfg.chatGptCfg.npc.batchSize = 10;
    }

    if (config.gramophoneCfg && config.gramophoneCfg.neDunTextCheckCfg) {
      //TODO modify
      const cfg = config.gramophoneCfg;

      cfg.neDunTextCheckCfg.businessId = "ebed131f9f74c0100d50a2fc19de6bbd";
      cfg.neDunTextCheckCfg.secretId = "66f0d0437dc2279987cee1e9992c7837";
      cfg.neDunTextCheckCfg.secretKey = "323ab91ba7cbb787729ce1934b8f75f3";
    }

    if (config.CronCheckUrl) {
      config.CronCheckUrl.dealDirtyLikeForwardComment ="";
    }

    config.roleMemSyncCfg.enable = false;

    if (config.momentCfg) {
      config.momentCfg.momentHotCronCheckUrl ="";
      config.momentCfg.momentReadCronCheckUrl ="";

      config.momentCfg.recommendFuxiUrl = "https://yeying-l36hmt-dev.apps-gcp-l36hmt.danlu.easebar.com/up/pyq_rec"

      config.momentCfg.getRoleInfoFromCn = true;
      config.momentCfg.cnRoleInfoUrl = "https://bt.hi.163.com/l36/player/getRoleInfo";
    }

    if (config.dataCenterCfg) {
      config.dataCenterCfg = {
        apiHost: "https://nshm-dc-test-17228-8080.apps-sl.danlu.netease.com/nshmdc",
        appid: "epgHGP5R0gveBokX",
        appsecret: "MSK2P3labCzJDWrYARmkKfUShrAbNn7h",
        timeout: 2000,
      };
    }
    config.kafkaMonitorCfg.receiver = "1599911"

    config.cors.origins = [
      /^https?:\/\/***************(:\d+)?/,
      /^https?:\/\/.*\.163\.com(:\d+)?/,
      /^https?:\/\/.*\.netease\.com(:\d+)?/,
      /^https?:\/\/.*\.swordofjustice\.com(:\d+)?/
    ]

    if (config.unionLoginCfg) {
      config.unionLoginCfg.cookieName = "nshmhmt_common_h5_account_token"
      config.unionLoginCfg.cookieKey = "secretTest";
      config.unionLoginCfg.cookieCfg = {
        path: "/",
        domain: ".swordofjustice.com",
        httpOnly: true,
        sameSite: "none",
        secure: true,
      }
    }

    if (config.server) {
      config.server.deployRegion = 'US'
    }


    if (config.clubCfg) {
      config.clubCfg.applyCommanderPreCheck = true;
      config.clubCfg.applyOperatorPreCheck = true;
      // 去掉玄机
      config.clubCfg.excludeJobIds = [3];
      // 申请选手最低等级放宽到20
      config.clubCfg.applyOperatorQualify.minLevel = 20
    }

    if(config.momentLotteryCfg){
      config.momentLotteryCfg.ignoreDrawingTime = true;
      config.momentLotteryCfg.minParticipateNumMultiForSet = 1;
      config.momentLotteryCfg.minLevelForParticipate = 1;
      config.momentLotteryCfg.minCreditScoreForParticipate = 0;

      config.momentLotteryCfg.maxDelCountForAdd = 1000;
      config.momentLotteryCfg.minDrawingDay = 1/24/60;
      config.momentLotteryCfg.maxCountForNotOpen = 1000;
      config.momentLotteryCfg.announceHour = 1/60;
    }

    if(config.transCfg){
      config.transCfg.transAssetsUrl = "http://l36-md-api-client-server-sea-release/l36/server/getTransAssetes"
    }

    module.exports = config;

  client-pm2.json: |
    {
      "apps": [
        {
          "name": "l36-api",
          "script": "/data/dist/app.js",
          "exec_mode": "cluster",
          "instances": 1,
          "env_production": {
            "NODE_ENV": "production"
          },
          "error_file": "/srv/logs/pm2_error.log",
          "out_file": "/srv/logs/pm2_name_out.log",
          "combine_logs": true,
          "autorestart": true,
          "max_restarts": 10
        },
        {
          "name": "l36-kafka-consumer",
          "script": "/data/run/consumeAllLogHmt.js",
          "env_production": {
              "NODE_ENV": "production"
          },
          "error_file": "/srv/logs/pm2_kafka_consumer.log",
          "out_file": "/srv/logs/pm2_kafka_consumer.log",
          "combine_logs": true,
          "autorestart": true,
          "max_restarts": 10
        },        
        {
            "name": "l36-cron",
            "script": "/data/dist/cron-jobs/crons.js",
            "env_production": {
                "NODE_ENV": "production"
            },
            "error_file": "/srv/logs/pm2_crons.log",
            "out_file": "/srv/logs/pm2_crons.log",
            "combine_logs": true,
            "autorestart": true,
            "max_restarts": 10
        },
        {
            "name": "l36-club-cron",
            "script": "/data/dist/cron-jobs/clubCron.js",
            "env_production": {
                "NODE_ENV": "production"
            },
            "error_file": "/srv/logs/pm2_club_crons.log",
            "out_file": "/srv/logs/pm2_club_crons.log",
            "combine_logs": true,
            "autorestart": true,
            "max_restarts": 10
        },

        {
            "name": "l36-moment-readCount-cron",
            "script": "/data/dist/cron-jobs/momentReadCount.js",
            "instances": 1,
            "env_production": {
                "NODE_ENV": "production"
            },
            "error_file": "/srv/logs/pm2_momentReadCount_crons.log",
            "out_file": "/srv/logs/pm2_momentReadCount_crons.log",
            "combine_logs": true,
            "autorestart": true,
            "max_restarts": 10
        },
        {
            "name": "l36-moment-hot-state-cron",
            "script": "/data/dist/cron-jobs/momentHot.js",
            "env_production": {
                "NODE_ENV": "production"
            },
            "error_file": "/srv/logs/pm2_momentHot_crons.log",
            "out_file": "/srv/logs/pm2_momentHot_crons.log",
            "combine_logs": true,
            "autorestart": true,
            "max_restarts": 10
        },
        {
          "name": "l36-fatal-crons",
          "script": "/data/dist/cron-jobs/fatalAlarm.js",
          "env_production": {
            "NODE_ENV": "production"
          },
          "error_file": "/srv/logs/pm2_fatal_crons.log",
          "out_file": "/srv/logs/pm2_fatal_crons.log",
          "combine_logs": true,
          "autorestart": true,
          "max_restarts": 10
        }
      ]
    }