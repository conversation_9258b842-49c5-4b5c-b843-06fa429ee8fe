apiVersion: apps/v1
kind: Deployment
metadata:
  name: l36-md-api-client-server-us-release
  namespace: l36sea
  labels:
    app: l36-md-api-client-server-us-release
spec:
  replicas: 1
  selector:
    matchLabels:
      app: l36-md-api-client-server-us-release
      group: node
      func: md
  template:
    metadata:
      labels:
        app: l36-md-api-client-server-us-release
        group: node
        func: md
    spec:
      imagePullSecrets:
        - name: regcred    
      volumes:
        - name: config-volume
          configMap:
            name: l36-md-api-us-release
            items:
              - key: config.js
                path: config.js
        - name: client-pm2-volume
          configMap:
            name: l36-md-api-us-release
            items:
              - key: client-pm2.json
                path: pm2.json
        - name: yunying-log
          hostPath:
            path: /srv/logs/pyq
            type: DirectoryOrCreate
      containers:
        - name: l36-md-api-server-us-release
          image: l36na-reg.leihuo.netease.com/ccc/l36-api:na_develop.359065.496bbf94
          ports:
            - name: http
              containerPort: 4001
              protocol: TCP
          readinessProbe:
            httpGet:
              path: /l36/healthCheck
              port: 4001
            initialDelaySeconds: 15
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
          livenessProbe:
            httpGet:
              path: /l36/healthCheck
              port: 4001
            initialDelaySeconds: 30
            periodSeconds: 20
            timeoutSeconds: 5
            failureThreshold: 5
          startupProbe:
            httpGet:
              path: /l36/healthCheck
              port: 4001
            initialDelaySeconds: 10
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 30              
          resources:
            requests:
              memory: "500Mi"
              cpu: "100m"
            limits:
              memory: "500Mi"
              cpu: "100m"
          volumeMounts:
            - name: config-volume
              mountPath: /data/dist/config/config.js
              subPath: config.js
            - name: client-pm2-volume
              mountPath: /data/bin/pm2.json
              subPath: pm2.json
            - name: yunying-log
              mountPath: /yunying
