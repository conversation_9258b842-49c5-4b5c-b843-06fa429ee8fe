apiVersion: v1
kind: ConfigMap
metadata:
  name: l36-md-api-us-release
  namespace: l36sea
data:
  config.js: |
    "use strict";
    const dConfig = require("../common/config.all");
    let config = Object.assign({}, dConfig);
    config.testCfg = {
      req_log: true,
      sync_log: true,
      test_env: false,
      debug: false,
      qa_debug: false,
      db_debug: false,
      redis_debug: false,
      request_debug: false,
      show_doc: false,
      skip_openId_auth: false,
      skip_ip_check: false,
      skip_level_check: true, //等级限制没了
      skip_skey_check: false,
    };

    config.db = {
      connectionLimit: 20,
      port: 3306,
      host: "rm-gs5p1rb1x9769740p.mysql.singapore.rds.aliyuncs.com",
      user: "nshm_us",
      password: "ccwfbsdycgyu",
      database: "l36_us",
      charset: "utf8mb4",
    };

    config.log = {
      dir: "/srv/logs",
      yunyingdir: "/yunying",
      level: "info",
      prefix: "l36_api",
      printInConsole: false,
      env: "l36_us_release",
      unlinkIntervalDays: 7,
    };
    config.slaveDb = config.db; //TODO modify

    config.commonCommentCfg.addCommentCD = 1;

    config.redis = {
      hosts: ["r-gs5e6d8b28cac374.redis.singapore.rds.aliyuncs.com"],
      host: "r-gs5e6d8b28cac374.redis.singapore.rds.aliyuncs.com",
      port: 6379,
      db: 0,
      password: "61DWNqUc6tRM5GLa",
      no_ready_check: false,
      keyPrefix: "l36_us_release:",
      enableAutoPipelining: true,
    };

    if (config.momentLotteryRedis) {
      config.momentLotteryRedis = config.redis;
    }

    config.openIdCfg.mode = 'complete'
    config.openIdCfg.clientId = '9e8440ccea7711eea3210242ac120002';
    config.openIdCfg.clientSecret = '2d8091a2df6244fcb46953dec43efe8f9e8443baea7711eea3210242ac120002';
    config.openIdCfg.cookieCfg = {
        path: '/',
        domain: '.swordofjustice.com',
        httpOnly: true,
        sameSite: 'none',
        secure: true
    }
    config.openIdCfg.redirectUri = "https://api-sea.swordofjustice.com/l36/npcAdmin/openIdCheck"

    config.FpCfg.devMode = false;
    config.FpCfg.project = "l36na-pyq"
    config.FpCfg.region = "EaseBar"
    config.FpCfg.secretKey = "ZfamTM1ZOkrs8zmvCjPckqN4R89YdGlS";
    config.FpCfg.callbackUrl = "http://l36-garden-api-server-server-us-release/l36/server/fpFileReviewCallbackWithoutDispatch";

    config.TopicCfg.listCacheSeconds = 60;

    config.mdCfg.openMinLevel = 0;

    config.removeRoleCommentDelAction = "phy";

    config.serverUrlCfg =
      "https://l36na.update.easebar.com/l36kr/serverlist/serverlist.txt";

    config.alarmPaoPao3Cfg.url = "http://file.mg.163.com/redmine/popomsgccc.php";

    config.dbSlowLogCfg.threshold = 5000;

    config.fatalAlarmCfg.ignoreRegArr = [
      /update[\s\S]+ set[\s\S]+where `ID` =/,
      /insert into `l36_roleInfo`[\s\S]+ on duplicate key update/,
      /insert into `l36_profile`[\s\S]+ on duplicate key update/,
      /l36_moment_like.idx_moment_role/,
      /ER_DUP_ENTRY/,
    ];

    config.chatbotQueueCfg.batchSize = 1;
    config.chatbotCfg.baseUrl = "https://linksrv-pyq-16863-80.apps-gcp-l36hmt.danlu.easebar.com/api/v1/dialogue";
    config.chatbotCfg.agent_id = "AG-984108530606800896";
    config.chatbotCfg.robot_id = "RT-905780754124910592";

    if (config.danmuCfg) {
      config.danmuCfg.danmuFileDir = "/yunying/danmu";
      config.danmuCfg.saveToMinio = true;

      // hmt不需要rsync同步文件，使用minio存储
      config.danmuCfg.cronCfg.RsyncDanmuCdnFile = false
      config.danmuCfg.rsyncFileGlob = "/yunying/danmu/danmu*.json";
      config.danmuCfg.rsyncPassword = "2IsXCBvz9Po0pmsr";
      config.danmuCfg.rsyncRemoteUrl = "root@l36-danmu-nginx:873/danmu/";
    }

    config.JWT_TOKEN_SECRET = "releaseForUS"

    if (config.kafka) {
      config.kafka.brokers = ["pulsar://aliyun01.brokers.pulsar.netease.com:6650"];
      config.kafka.topic = "persistent://game/game/l36na_pulsar";
      config.kafka.username = "";
      config.kafka.password = "";
      config.kafka.logReg = /\[(.*?)\]\[(.*?)\],(\{.*\})/;
      config.kafka.openOnlineServerLimit = false;
      config.kafka.useClient = 'pulsar';
      config.kafka.syncApiUrl="https://bt.hi.163.com/l36/kafka/consumer"
    }

    config.MIN_SERVER_ID_IN_PROD = 0


    config.ipNetCfg.token = "e1d1206616f111eeb2f1525400aa5830";
    config.ipNetCfg.url = "http://ip-info-service/api/getIpInfo";
    config.envSdkUrl = "http://l36-envsdk-server-release:80";

    config.gameServerIpWhiteList = [
      '************/32',   '**********/32',     '************/32',
      '************/32',   '***********/32',    '************/32',
      '***********/32',    '*************/32',  '**************/32',
      '**************/32', '************/32',   '**************/32',
      '*************/32',  '*************/32',  '*************/31',
      '**************/32', '*************/32',  '**************/32',
      '************/32',   '************/32',   '************/32',
      '************/32',   '***********/32',    '***********/32',
      '*************/32',  '**************/32', '***********',
      '**************',    '************/31',   '**********/32',
      '**********/24',     '***********/31',    '***********/31',
      '**********/32',     '**********/32',     '*********/24',
      '************/32',   '***********/32',    '***********/32',
      '***********/32',    '************/32',   '************/32',
      '************/32',   '************/31',   '10.9.83.13/32',
      '10.120.173.204/32', '10.170.23.86/32',   '10.170.23.88/32',
      '10.202.22.32/31',   '10.202.22.50/31',   '10.214.5.67/32',
      '10.214.5.70/32',    '10.214.5.74/31',    '10.214.5.76/32',
      '10.214.5.80/32',    '10.214.5.83/32',    '10.214.5.84/30',
      '10.214.5.91/32',    '10.214.5.92/30',    '10.214.5.96/32',
      '45.253.180.98/32',  '47.74.39.63/32',    '47.74.43.40/32',
      '59.111.129.43/32',
    ]

    if (config.friendModelCfg) {
      config.friendModelCfg.enable = true;
    }

    if (config.CronTaskSwitch) {
      config.CronTaskSwitch.AuditDanmuByFriendModel = true;
    }

    if (config.commonCommentAdminCfg && config.commonCommentAdminCfg.chatGptCfg) {
      //TODO modify
      config.commonCommentAdminCfg.chatGptCfg.npc.agentId =
        "AG-l36-homepage-comment-prod";
      config.commonCommentAdminCfg.chatGptCfg.npc.url =
        "https://linksrv-homepage-pro-11238-80.apps-cae.danlu.netease.com/text_response";
      config.commonCommentAdminCfg.chatGptCfg.npc.batchSize = 10;
    }

    if (config.gramophoneCfg && config.gramophoneCfg.neDunTextCheckCfg) {
      //TODO modify
      const cfg = config.gramophoneCfg;

      cfg.neDunTextCheckCfg.businessId = "ebed131f9f74c0100d50a2fc19de6bbd";
      cfg.neDunTextCheckCfg.secretId = "66f0d0437dc2279987cee1e9992c7837";
      cfg.neDunTextCheckCfg.secretKey = "323ab91ba7cbb787729ce1934b8f75f3";
    }

    if (config.CronCheckUrl) {
      config.CronCheckUrl.dealDirtyLikeForwardComment = "";
    }

    config.roleMemSyncCfg.enable = false;

    if (config.momentCfg) {
      config.momentCfg.momentHotCronCheckUrl = "";
      config.momentCfg.momentReadCronCheckUrl ="";

      config.momentCfg.recommendFuxiUrl = "https://yeying-gateway.apps-gcp-l36hmt.danlu.easebar.com/up/pyq_rec"
    }

    if (config.dataCenterCfg) {
      config.dataCenterCfg = {
        apiHost: "http://nshm-dc-hmt-server-online.ccc/nshmdc",
        appid: "epgHGP5R0gveBokX",
        appsecret: "MSK2P3labCzJDWrYARmkKfUShrAbNn7h",
        timeout: 2000,
      };
    }
    config.kafkaMonitorCfg.receiver = "1599911"

    if (config.unionLoginCfg) {
      config.unionLoginCfg.cookieName = "nshmhmt_common_h5_account_token"
      config.unionLoginCfg.cookieKey = "hwRj8nIqEri4M8gqxknABUC2BJvoblGn";
      config.unionLoginCfg.cookieCfg = {
        path: "/",
        domain: ".swordofjustice.com",
        httpOnly: true,
        sameSite: "none",
        secure: true,
      }
    }


    config.cors.origins = [
      /^https?:\/\/***************(:\d+)?/,
      /^https?:\/\/.*\.163\.com(:\d+)?/,
      /^https?:\/\/.*\.netease\.com(:\d+)?/,
      /^https?:\/\/.*\.swordofjustice\.com(:\d+)?/
    ]

    if (config.minioCfg) {
      // host ********** l36hmt-ccc-s3.leihuo.netease.com
      config.minioCfg.endPoint = "**********";
      config.minioCfg.accessKey = "1NyFOrujO0k1K8uTlbjQ";
      config.minioCfg.secretKey = "TpU2lEqhhxdfp6XyON05jDxAVPUi2Rcw5IQw9SvF";
    }


    if (config.server) {
      config.server.deployRegion = 'US'
    }


    if (config.clubCfg) {
      // 去掉玄机
      config.clubCfg.excludeJobIds = [3];

      // 使用pulsar消费
      config.clubCfg.kafkaCfg.consumerType = 'pulsar'
    }

    if(config.transCfg){
      config.transCfg.transAssetsUrl = "http://l36-md-api-client-server-sea-release/l36/server/getTransAssetes"
    }

    config.Features.momentVisit = true;
    config.Features.momentLottery = true;
    module.exports = config;

  client-pm2.json: |
    {
      "apps": [
        {
          "name": "l36-api",
          "script": "/data/dist/app.js",
          "exec_mode": "cluster",
          "instances": 1,
          "env_production": {
            "NODE_ENV": "production"
          },
          "error_file": "/srv/logs/pm2_error.log",
          "out_file": "/srv/logs/pm2_name_out.log",
          "combine_logs": true,
          "autorestart": true,
          "max_restarts": 10
        },
        {
          "name": "l36-fatal-crons",
          "script": "/data/dist/cron-jobs/fatalAlarm.js",
          "env_production": {
            "NODE_ENV": "production"
          },
          "error_file": "/srv/logs/pm2_fatal_crons.log",
          "out_file": "/srv/logs/pm2_fatal_crons.log",
          "combine_logs": true,
          "autorestart": true,
          "max_restarts": 10
        }
      ]
    }
  kafka-sync-pm2.json: |
    {
        "apps": [
            {
                "name": "l36-kafka-consumer",
                "script": "/data/run/consumeLog.js",
                "env_production": {
                    "NODE_ENV": "production"
                },
                "error_file": "/srv/logs/pm2_kafka_consumer.log",
                "out_file": "/srv/logs/pm2_kafka_consumer.log",
                "combine_logs": true,
                "autorestart": true,
                "max_restarts": 10
            },
            {
                "name": "l36-fatal-crons",
                "script": "/data/dist/cron-jobs/fatalAlarm.js",
                "env_production": {
                    "NODE_ENV": "production"
                },
                "error_file": "/srv/logs/pm2_fatal_crons.log",
                "out_file": "/srv/logs/pm2_fatal_crons.log",
                "combine_logs": true,
                "autorestart": true,
                "max_restarts": 10
            },
            {
                "name": "l36-api",
                "script": "/data/dist/app.js",
                "exec_mode": "cluster",
                "instances": 1,
                "env_production": {
                    "NODE_ENV": "production"
                },
                "error_file": "/srv/logs/pm2_error.log",
                "out_file": "/srv/logs/pm2_name_out.log",
                "combine_logs": true,
                "autorestart": true,
                "max_restarts": 10
            }
        ]
    }
  kafka-sync-to-cn-pm2.json: |
    {
        "apps": [
            {
                "name": "l36-kafka-consumer-to-cn",
                "script": "/data/run/consumeAllLog2CnTest.js",
                "env_production": {
                    "NODE_ENV": "production"
                },
                "error_file": "/srv/logs/pm2_kafka_consumer_to_cn.log",
                "out_file": "/srv/logs/pm2_kafka_consumer_to_cn.log",
                "combine_logs": true,
                "autorestart": true,
                "max_restarts": 10
            },
            {
                "name": "l36-fatal-crons",
                "script": "/data/dist/cron-jobs/fatalAlarm.js",
                "env_production": {
                    "NODE_ENV": "production"
                },
                "error_file": "/srv/logs/pm2_fatal_crons.log",
                "out_file": "/srv/logs/pm2_fatal_crons.log",
                "combine_logs": true,
                "autorestart": true,
                "max_restarts": 10
            },
            {
                "name": "l36-api",
                "script": "/data/dist/app.js",
                "exec_mode": "cluster",
                "instances": 1,
                "env_production": {
                    "NODE_ENV": "production"
                },
                "error_file": "/srv/logs/pm2_error.log",
                "out_file": "/srv/logs/pm2_name_out.log",
                "combine_logs": true,
                "autorestart": true,
                "max_restarts": 10
            }
        ]
    }

  kafka-sync-role-pm2.json: |
    {
        "apps": [
            {
                "name": "l36-kafka-consumer-role",
                "script": "/data/run/consumeLogRole.js",
                "env_production": {
                    "NODE_ENV": "production"
                },
                "error_file": "/srv/logs/pm2_sync.log",
                "out_file": "/srv/logs/pm2_sync.log",
                "combine_logs": true,
                "autorestart": true,
                "max_restarts": 10
            },
            {
                "name": "l36-fatal-crons",
                "script": "/data/dist/cron-jobs/fatalAlarm.js",
                "env_production": {
                    "NODE_ENV": "production"
                },
                "error_file": "/srv/logs/pm2_fatal_crons.log",
                "out_file": "/srv/logs/pm2_fatal_crons.log",
                "combine_logs": true,
                "autorestart": true,
                "max_restarts": 10
            },
            {
                "name": "l36-api",
                "script": "/data/dist/app.js",
                "exec_mode": "cluster",
                "instances": 1,
                "env_production": {
                    "NODE_ENV": "production"
                },
                "error_file": "/srv/logs/pm2_error.log",
                "out_file": "/srv/logs/pm2_name_out.log",
                "combine_logs": true,
                "autorestart": true,
                "max_restarts": 10
            }
        ]
    }

  cron-pm2.json: |
    {
      "apps": [
        {
          "name": "l36-api",
          "script": "/data/dist/app.js",
          "exec_mode": "cluster",
          "instances": 1,
          "env_production": {
            "NODE_ENV": "production"
          },
          "error_file": "/srv/logs/pm2_error.log",
          "out_file": "/srv/logs/pm2_name_out.log",
          "combine_logs": true,
          "autorestart": true,
          "max_restarts": 10
        },
        {
            "name": "l36-cron",
            "script": "/data/dist/cron-jobs/crons.js",
            "env_production": {
                "NODE_ENV": "production"
            },
            "error_file": "/srv/logs/pm2_crons.log",
            "out_file": "/srv/logs/pm2_crons.log",
            "combine_logs": true,
            "autorestart": true,
            "max_restarts": 10
        },
        {
          "name": "hmt-nshm-club-cron-release",
          "script": "/data/dist/cron-jobs/clubCron.js",
          "error_file": "/srv/logs/pm2_hmt_nshm_club_cron.log",
          "out_file": "/srv/logs/pm2_hmt_nshm_club_cron.log",
          "instances": 1,
          "production": {
            "NODE_ENV": "production"
          },
          "combine_logs": true,
          "autorestart": true,
          "max_restarts": 10
        },
        {
            "name": "l36-danmu-cut-cdn-crons",
            "script": "/data/dist/cron-jobs/danmuCut.js",
            "env_production": {
                "NODE_ENV": "production"
            },
            "error_file": "/srv/logs/pm2_danmu_cut_crons.log",
            "out_file": "/srv/logs/pm2_danmu_cut_crons.log",
            "combine_logs": true,
            "autorestart": true,
            "max_restarts": 10
        },
        {
            "name": "l36-clear-inform",
            "script": "/data/run/clearInformByPerRole.js",
            "env_production": {
                "NODE_ENV": "production"
            },
            "error_file": "/srv/logs/pm2_sync.log",
            "out_file": "/srv/logs/pm2_sync.log",
            "combine_logs": true,
            "autorestart": true,
            "max_restarts": 10
        },
        {
            "name": "l36-moment-readCount-cron",
            "script": "/data/dist/cron-jobs/momentReadCount.js",
            "instances": 1,
            "env_production": {
                "NODE_ENV": "production"
            },
            "error_file": "/srv/logs/pm2_momentReadCount_crons.log",
            "out_file": "/srv/logs/pm2_momentReadCount_crons.log",
            "combine_logs": true,
            "autorestart": true,
            "max_restarts": 10
        },
        {
            "name": "l36-moment-hot-state-cron",
            "script": "/data/dist/cron-jobs/momentHot.js",
            "env_production": {
                "NODE_ENV": "production"
            },
            "error_file": "/srv/logs/pm2_momentHot_crons.log",
            "out_file": "/srv/logs/pm2_momentHot_crons.log",
            "combine_logs": true,
            "autorestart": true,
            "max_restarts": 10
        },
        {
          "name": "l36-fatal-crons",
          "script": "/data/dist/cron-jobs/fatalAlarm.js",
          "env_production": {
            "NODE_ENV": "production"
          },
          "error_file": "/srv/logs/pm2_fatal_crons.log",
          "out_file": "/srv/logs/pm2_fatal_crons.log",
          "combine_logs": true,
          "autorestart": true,
          "max_restarts": 10
        }
      ]
    }
  club-consumer-pm2.json: |
    {
        "apps": [
            {
                "name": "hmt-nshm-club-consumer-match-release",
                "script": "/data/dist/run/gameClubMatchLogConsumer.js",
                "production": {
                    "NODE_ENV": "production"
                },
                "error_file": "/srv/logs/pm2_club_consumer_match_error.log",
                "out_file": "/srv/logs/pm2_club_consumer_match_out.log",
                "combine_logs": true,
                "autorestart": true,
                "max_restarts": 10
            },
            {
                "name": "hmt-nshm-club-consumer-other-release",
                "script": "/data/dist/run/gameClubOtherLogConsumer.js",
                "production": {
                    "NODE_ENV": "production"
                },
                "error_file": "/srv/logs/pm2_club_consumer_other_error.log",
                "out_file": "/srv/logs/pm2_club_consumer_other_out.log",
                "combine_logs": true,
                "autorestart": true,
                "max_restarts": 10
            }
        ]
    }