# https://kubernetes.io/docs/concepts/services-networking/ingress/#the-ingress-resource

apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: test-kr-ingress
  namespace: l36sea
  annotations:
    nginx.ingress.kubernetes.io/use-regex: "true"
spec:
  ingressClassName: nginx
  tls:
    - hosts:
        - "preview-kr.swordofjustice.com"
      secretName: swordofjustice-tls-preview
  rules:
    - host: preview-kr.swordofjustice.com
      http:
        paths:
          - path: /l36/
            pathType: Prefix
            backend:
              service:
                name: l36-md-api-client-server-kr-preview
                port:
                  number: 80
          - path: /l36/gardenStation/
            pathType: Prefix
            backend:
              service:
                name: l36-garden-api-server-kr-test
                port:
                  number: 80
          - path: /l36/fashion/
            pathType: Prefix
            backend:
              service:
                name: l36-fashion-api-server-kr-test
                port:
                  number: 80