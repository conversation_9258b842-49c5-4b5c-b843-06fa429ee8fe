apiVersion: apps/v1
kind: Deployment
metadata:
  name: mysql-test
  namespace: l36sea
  labels:
    app: mysql-test
spec:
  replicas: 1
  selector:
    matchLabels:
      app: mysql-test
      group: node
      func: md
  template:
    metadata:
      labels:
        app: mysql-test
        group: node
        func: md
    spec:
      imagePullSecrets:
          - name: regcred            
      containers:
        - name: mysql-test
          image: l36na-reg.leihuo.netease.com/ccc/mysql:5.7.8
          ports:
            - name: http
              containerPort: 3306
              protocol: TCP
          resources:
            requests:
              memory: "200Mi"
              cpu: "50m"
            limits:
              memory: "200Mi"
              cpu: "50m"
          env:
            - name: MYSQL_ROOT_PASSWORD
              value: nodePassword

---
apiVersion: v1
kind: Service
metadata:
  name: mysql-test-svc
  namespace: l36sea
  labels:
    app: mysql-test
spec:
  selector:
    app: mysql-test # 确保这个 selector 与 Deployment 的 Pod 标签匹配
  type: NodePort
  sessionAffinity: None
  ports:
    - protocol: TCP
      port: 3306       # Service 监听的端口
      targetPort: 3306 # 转发到 Pod 的端口 (与 containerPort 匹配)
