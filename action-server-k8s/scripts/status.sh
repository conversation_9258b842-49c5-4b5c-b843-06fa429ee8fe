#!/bin/bash

# Action Server 状态检查脚本
# 使用方法: ./status.sh [region] [environment]

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查单个部署状态
check_deployment_status() {
    local region="$1"
    local env="$2"

    local namespace="default"
    local deployment_name="$region-$env-action-server"

    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo -e "${BLUE}检查 $region-$env 状态${NC}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

    log_info "使用命名空间: $namespace"

    # 检查部署
    if ! kubectl get deployment "$deployment_name" -n "$namespace" &> /dev/null; then
        log_error "部署不存在: $deployment_name"
        return 1
    fi

    # 获取部署状态
    local deployment_status=$(kubectl get deployment "$deployment_name" -n "$namespace" -o jsonpath='{.status.conditions[?(@.type=="Available")].status}')
    local ready_replicas=$(kubectl get deployment "$deployment_name" -n "$namespace" -o jsonpath='{.status.readyReplicas}')
    local desired_replicas=$(kubectl get deployment "$deployment_name" -n "$namespace" -o jsonpath='{.spec.replicas}')

    echo
    log_info "部署状态:"
    kubectl get deployment "$deployment_name" -n "$namespace"

    if [[ "$deployment_status" == "True" ]] && [[ "$ready_replicas" == "$desired_replicas" ]]; then
        log_success "部署状态正常 ($ready_replicas/$desired_replicas 副本就绪)"
    else
        log_warning "部署状态异常 ($ready_replicas/$desired_replicas 副本就绪)"
    fi

    # 检查 Pod 状态
    echo
    log_info "Pod 状态:"
    kubectl get pods -n "$namespace" -l app=action-server

    # 检查服务
    echo
    log_info "服务状态:"
    kubectl get service -n "$namespace" -l app=action-server

    # 检查 Ingress
    echo
    log_info "Ingress 状态:"
    if kubectl get ingress -n "$namespace" &> /dev/null; then
        kubectl get ingress -n "$namespace"
    else
        log_warning "未找到 Ingress 资源"
    fi

    # 检查 HPA
    echo
    log_info "HPA 状态:"
    local hpa_name="$region-$env-action-server-hpa"
    if kubectl get hpa "$hpa_name" -n "$namespace" &> /dev/null; then
        kubectl get hpa "$hpa_name" -n "$namespace"
    else
        log_warning "未找到 HPA 资源: $hpa_name"
    fi

    # 检查最近的事件
    echo
    log_info "最近事件:"
    kubectl get events -n "$namespace" --sort-by='.lastTimestamp' | tail -5

    # 健康检查
    echo
    log_info "健康检查:"
    local domain_suffix=""
    if [[ "$env" == "preview" ]]; then
        domain_suffix="-preview"
    fi
    local health_url="https://action-server-$region$domain_suffix.swordofjustice.com/health"

    if command -v curl &> /dev/null; then
        if curl -s --max-time 10 "$health_url" > /dev/null; then
            log_success "健康检查通过: $health_url"
        else
            log_warning "健康检查失败: $health_url"
        fi
    else
        log_info "未安装 curl，跳过健康检查"
    fi

    echo
}

# 检查所有部署状态
check_all_status() {
    local regions=("sea" "eu" "jp" "kr" "us")
    local environments=("preview" "release")

    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo -e "${BLUE}Action Server 全局状态概览${NC}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

    printf "%-15s %-10s %-15s %-10s %-15s\n" "Region" "Env" "Namespace" "Status" "Replicas"
    echo "────────────────────────────────────────────────────────────────────────────"

    for region in "${regions[@]}"; do
        for env in "${environments[@]}"; do
            local namespace="default"
            local deployment_name="$region-$env-action-server"

            if kubectl get deployment "$deployment_name" -n "$namespace" &> /dev/null; then
                local status=$(kubectl get deployment "$deployment_name" -n "$namespace" -o jsonpath='{.status.conditions[?(@.type=="Available")].status}')
                local ready=$(kubectl get deployment "$deployment_name" -n "$namespace" -o jsonpath='{.status.readyReplicas}')
                local desired=$(kubectl get deployment "$deployment_name" -n "$namespace" -o jsonpath='{.spec.replicas}')

                local status_display="❌ Failed"
                if [[ "$status" == "True" ]] && [[ "$ready" == "$desired" ]]; then
                    status_display="✅ Ready"
                elif [[ "$ready" -gt 0 ]]; then
                    status_display="⚠️  Partial"
                fi

                printf "%-15s %-10s %-15s %-10s %-15s\n" "$region" "$env" "$namespace" "$status_display" "$ready/$desired"
            else
                printf "%-15s %-10s %-15s %-10s %-15s\n" "$region" "$env" "$namespace" "❌ No Deploy" "N/A"
            fi
        done
    done

    echo
}

# 显示帮助
show_help() {
    cat << EOF
Action Server 状态检查脚本

使用方法:
    $0                      # 显示所有部署的状态概览
    $0 <region>             # 显示指定地区所有环境的详细状态
    $0 <region> <env>       # 显示指定地区和环境的详细状态

参数:
    region    地区 (sea, eu, jp, kr, us)
    env       环境 (preview, release)

示例:
    $0                      # 显示全局状态概览
    $0 sea                  # 显示 SEA 地区详细状态
    $0 sea preview          # 显示 SEA preview 环境详细状态

EOF
}

# 检查集群连接
check_cluster_connection() {
    # 检查 kubectl
    if ! command -v kubectl &> /dev/null; then
        log_error "缺少依赖: kubectl"
        exit 1
    fi

    log_info "Kubernetes 集群连接正常"
}

# 主函数
main() {
    check_cluster_connection

    case $# in
        0)
            check_all_status
            ;;
        1)
            local region="$1"
            if [[ "$region" == "--help" || "$region" == "-h" ]]; then
                show_help
                exit 0
            fi

            log_info "检查地区: $region"
            check_deployment_status "$region" "preview"
            check_deployment_status "$region" "release"
            ;;
        2)
            local region="$1"
            local env="$2"
            check_deployment_status "$region" "$env"
            ;;
        *)
            log_error "参数过多"
            show_help
            exit 1
            ;;
    esac
}

main "$@"
