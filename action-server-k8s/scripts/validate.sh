#!/bin/bash

# Action Server 配置验证脚本
# 使用方法: ./validate.sh [region] [environment]

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$SCRIPT_DIR/.."

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 验证配置文件格式
validate_config_file() {
    local config_file="$1"
    local region="$2"
    local env="$3"

    if [[ ! -f "$config_file" ]]; then
        log_error "配置文件不存在: $config_file"
        return 1
    fi

    log_info "验证配置文件: $config_file"

    # 检查基本的 TOML 语法
    if ! command -v toml &> /dev/null; then
        log_warning "未安装 toml 工具，跳过语法检查"
    else
        if ! toml validate "$config_file" &> /dev/null; then
            log_error "TOML 语法错误: $config_file"
            return 1
        fi
    fi

    # 检查必需的配置项
    local required_sections=("App" "Mysql" "Redis" "Server")
    for section in "${required_sections[@]}"; do
        if ! grep -q "^\[$section\]" "$config_file"; then
            log_error "缺少必需的配置节: [$section] in $config_file"
            return 1
        fi
    done

    # 检查地区特定配置
    if ! grep -q "DBName.*$region" "$config_file"; then
        log_warning "数据库名称可能不匹配地区: $region"
    fi

    if ! grep -q "Prefix.*$region.*$env" "$config_file"; then
        log_warning "Redis 前缀可能不匹配地区和环境: $region-$env"
    fi

    log_success "配置文件验证通过: $config_file"
    return 0
}

# 验证 Kubernetes 清单
validate_k8s_manifests() {
    local overlay_path="$1"
    local region="$2"
    local env="$3"

    if [[ ! -d "$overlay_path" ]]; then
        log_error "覆盖目录不存在: $overlay_path"
        return 1
    fi

    log_info "验证 Kubernetes 清单: $overlay_path"

    # 检查必需文件
    local required_files=("kustomization.yaml" "deployment-patch.yaml" "ingress.yaml")
    for file in "${required_files[@]}"; do
        if [[ ! -f "$overlay_path/$file" ]]; then
            log_error "缺少必需文件: $overlay_path/$file"
            return 1
        fi
    done

    # 验证 Kustomize 配置
    if ! kubectl kustomize "$overlay_path" > /dev/null; then
        log_error "Kustomize 配置验证失败: $overlay_path"
        return 1
    fi

    # 验证生成的清单
    local temp_manifest=$(mktemp)
    if ! kubectl kustomize "$overlay_path" > "$temp_manifest" 2>/dev/null; then
        log_error "Kustomize 生成清单失败: $overlay_path"
        rm -f "$temp_manifest"
        return 1
    fi

    if ! kubectl apply --dry-run=client -f "$temp_manifest" > /dev/null 2>&1; then
        log_warning "Kubernetes 清单验证有警告，但语法正确: $overlay_path"
        # 不返回错误，因为可能只是警告
    fi

    rm -f "$temp_manifest"
    log_success "Kubernetes 清单验证通过: $overlay_path"
    return 0
}

# 验证网络连接
validate_connectivity() {
    local region="$1"
    local env="$2"
    local config_file="$PROJECT_ROOT/configs/action-server/$region/${env}-config.toml"

    log_info "验证网络连接性"

    # 提取数据库主机
    local db_host=$(grep "^Host.*=" "$config_file" | head -1 | cut -d'"' -f2)
    if [[ -n "$db_host" ]]; then
        log_info "检查数据库连接: $db_host"
        if timeout 5 bash -c "</dev/tcp/$db_host/3306" 2>/dev/null; then
            log_success "数据库连接正常: $db_host"
        else
            log_warning "数据库连接失败: $db_host"
        fi
    fi

    # 提取 Redis 主机
    local redis_addr=$(grep "^Addr.*=" "$config_file" | cut -d'"' -f2)
    if [[ -n "$redis_addr" ]]; then
        local redis_host=$(echo "$redis_addr" | cut -d':' -f1)
        local redis_port=$(echo "$redis_addr" | cut -d':' -f2)
        log_info "检查 Redis 连接: $redis_host:$redis_port"
        if timeout 5 bash -c "</dev/tcp/$redis_host/$redis_port" 2>/dev/null; then
            log_success "Redis 连接正常: $redis_host:$redis_port"
        else
            log_warning "Redis 连接失败: $redis_host:$redis_port"
        fi
    fi
}

# 验证单个地区环境
validate_single() {
    local region="$1"
    local env="$2"

    log_info "验证 $region-$env 配置"

    local config_file="$PROJECT_ROOT/configs/action-server/$region/${env}-config.toml"
    local overlay_path="$PROJECT_ROOT/overlays/$region/$env"

    local errors=0

    # 验证配置文件
    if ! validate_config_file "$config_file" "$region" "$env"; then
        ((errors++))
    fi

    # 验证 K8s 清单
    if ! validate_k8s_manifests "$overlay_path" "$region" "$env"; then
        ((errors++))
    fi

    # 验证网络连接
    validate_connectivity "$region" "$env"

    if [[ $errors -eq 0 ]]; then
        log_success "$region-$env 验证通过"
        return 0
    else
        log_error "$region-$env 验证失败 ($errors 个错误)"
        return 1
    fi
}

# 验证所有配置
validate_all() {
    local regions=("sea" "eu" "jp" "kr" "us")
    local environments=("preview" "release")
    local total_errors=0

    log_info "开始验证所有配置"

    for region in "${regions[@]}"; do
        for env in "${environments[@]}"; do
            echo
            if ! validate_single "$region" "$env"; then
                ((total_errors++))
            fi
        done
    done

    echo
    if [[ $total_errors -eq 0 ]]; then
        log_success "所有配置验证通过!"
    else
        log_error "验证完成，共 $total_errors 个错误"
        exit 1
    fi
}

# 显示帮助
show_help() {
    cat << EOF
Action Server 配置验证脚本

使用方法:
    $0                      # 验证所有地区和环境
    $0 <region>             # 验证指定地区的所有环境
    $0 <region> <env>       # 验证指定地区和环境

参数:
    region    地区 (sea, eu, jp, kr, us)
    env       环境 (preview, release)

示例:
    $0                      # 验证所有配置
    $0 sea                  # 验证 SEA 地区所有环境
    $0 sea preview          # 验证 SEA preview 环境

EOF
}

# 主函数
main() {
    case $# in
        0)
            validate_all
            ;;
        1)
            local region="$1"
            if [[ "$region" == "--help" || "$region" == "-h" ]]; then
                show_help
                exit 0
            fi

            log_info "验证地区: $region"
            local errors=0
            if ! validate_single "$region" "preview"; then
                ((errors++))
            fi
            echo
            if ! validate_single "$region" "release"; then
                ((errors++))
            fi

            if [[ $errors -eq 0 ]]; then
                log_success "地区 $region 验证通过"
            else
                log_error "地区 $region 验证失败"
                exit 1
            fi
            ;;
        2)
            local region="$1"
            local env="$2"
            validate_single "$region" "$env"
            ;;
        *)
            log_error "参数过多"
            show_help
            exit 1
            ;;
    esac
}

main "$@"
