#!/bin/bash

# Action Server 部署脚本
# 使用方法: ./deploy.sh <region> <environment> [options]
# 示例: ./deploy.sh sea preview --dry-run

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$SCRIPT_DIR/.."

# 默认参数
REGION=""
ENVIRONMENT=""
DRY_RUN=false
FORCE=false
VERBOSE=false
IMAGE_TAG=""

# 支持的地区和环境
VALID_REGIONS=("sea" "eu" "jp" "kr" "us")
VALID_ENVIRONMENTS=("preview" "release")

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
Action Server 部署脚本

使用方法:
    $0 <region> <environment> [options]

参数:
    region          目标地区 (sea, eu, jp, kr, us)
    environment     目标环境 (preview, release)

选项:
    --dry-run       只显示将要执行的操作，不实际部署
    --force         强制部署，跳过确认
    --verbose       显示详细输出
    --image-tag     指定镜像标签 (默认: preview/latest)
    --help          显示此帮助信息

示例:
    $0 sea preview                    # 部署到 SEA preview 环境
    $0 eu release --dry-run          # 预览 EU release 部署
    $0 us release --force            # 强制部署到 US release
    $0 jp preview --image-tag v1.2.3 # 使用指定镜像标签部署

EOF
}

# 验证参数
validate_args() {
    if [[ -z "$REGION" ]]; then
        log_error "缺少地区参数"
        show_help
        exit 1
    fi

    if [[ -z "$ENVIRONMENT" ]]; then
        log_error "缺少环境参数"
        show_help
        exit 1
    fi

    if [[ ! " ${VALID_REGIONS[@]} " =~ " ${REGION} " ]]; then
        log_error "无效的地区: $REGION"
        log_info "支持的地区: ${VALID_REGIONS[*]}"
        exit 1
    fi

    if [[ ! " ${VALID_ENVIRONMENTS[@]} " =~ " ${ENVIRONMENT} " ]]; then
        log_error "无效的环境: $ENVIRONMENT"
        log_info "支持的环境: ${VALID_ENVIRONMENTS[*]}"
        exit 1
    fi
}

# 检查依赖
check_dependencies() {
    local deps=("kubectl")

    for dep in "${deps[@]}"; do
        if ! command -v "$dep" &> /dev/null; then
            log_error "缺少依赖: $dep"
            exit 1
        fi
    done

    # 检查 kustomize（可选，kubectl 内置）
    if ! command -v "kustomize" &> /dev/null; then
        log_warning "kustomize 未安装，将使用 kubectl 内置版本"
    fi

    # 检查 kubectl 连接和权限
    if ! kubectl auth can-i create deployments &> /dev/null; then
        log_warning "当前用户可能没有部署权限，请确认权限配置"
        # 尝试基本连接测试
        if ! kubectl get namespaces &> /dev/null; then
            log_error "无法连接到 Kubernetes 集群或权限不足"
            exit 1
        fi
    fi

    log_info "依赖检查通过"
}

# 确认部署
confirm_deployment() {
    if [[ "$FORCE" == "true" ]]; then
        return 0
    fi

    echo
    log_warning "即将部署到以下环境:"
    echo "  地区: $REGION"
    echo "  环境: $ENVIRONMENT"
    echo "  命名空间: default"
    if [[ -n "$IMAGE_TAG" ]]; then
        echo "  镜像标签: $IMAGE_TAG"
    fi
    echo

    read -p "确认继续部署? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "部署已取消"
        exit 0
    fi
}

# 执行部署
deploy() {
    local overlay_path="$PROJECT_ROOT/overlays/$REGION/$ENVIRONMENT"

    if [[ ! -d "$overlay_path" ]]; then
        log_error "找不到覆盖配置: $overlay_path"
        exit 1
    fi

    log_info "开始部署 action-server 到 $REGION-$ENVIRONMENT"

    # 如果指定了镜像标签，临时修改 kustomization.yaml
    local temp_kustomization=""
    if [[ -n "$IMAGE_TAG" ]]; then
        temp_kustomization=$(mktemp)
        cp "$overlay_path/kustomization.yaml" "$temp_kustomization"

        # 更新镜像标签
        sed -i "s/newTag: .*/newTag: $IMAGE_TAG/" "$overlay_path/kustomization.yaml"
    fi

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "执行 dry-run 模式"
        kubectl kustomize "$overlay_path"
    else
        # 实际部署
        if [[ "$VERBOSE" == "true" ]]; then
            kubectl apply -k "$overlay_path" --validate=true
        else
            kubectl apply -k "$overlay_path" --validate=true > /dev/null
        fi

        # 等待部署完成
        local namespace="default"
        local deployment_name="$REGION-$ENVIRONMENT-action-server"

        log_info "等待部署完成..."
        kubectl rollout status deployment/"$deployment_name" -n "$namespace" --timeout=300s

        # 检查 Pod 状态
        log_info "检查 Pod 状态..."
        kubectl get pods -n "$namespace" -l app=action-server

        log_success "部署完成!"

        # 显示访问地址
        local domain_suffix=""
        if [[ "$ENVIRONMENT" == "preview" ]]; then
            domain_suffix="-preview"
        fi
        log_info "访问地址: https://action-server-$REGION$domain_suffix.swordofjustice.com"
    fi

    # 恢复原始 kustomization.yaml
    if [[ -n "$temp_kustomization" ]]; then
        mv "$temp_kustomization" "$overlay_path/kustomization.yaml"
    fi
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --force)
            FORCE=true
            shift
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        --image-tag)
            IMAGE_TAG="$2"
            shift 2
            ;;
        --help)
            show_help
            exit 0
            ;;
        -*)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
        *)
            if [[ -z "$REGION" ]]; then
                REGION="$1"
            elif [[ -z "$ENVIRONMENT" ]]; then
                ENVIRONMENT="$1"
            else
                log_error "过多的参数: $1"
                show_help
                exit 1
            fi
            shift
            ;;
    esac
done

# 主流程
main() {
    validate_args
    check_dependencies

    if [[ "$DRY_RUN" != "true" ]]; then
        confirm_deployment
    fi

    deploy
}

main "$@"
