#!/bin/bash

# Action Server 回滚脚本
# 使用方法: ./rollback.sh <region> <environment> [options]

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 默认参数
REGION=""
ENVIRONMENT=""
REVISION=""
FORCE=false
LIST_REVISIONS=false

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助
show_help() {
    cat << EOF
Action Server 回滚脚本

使用方法:
    $0 <region> <environment> [options]

参数:
    region          目标地区 (sea, eu, jp, kr, us)
    environment     目标环境 (preview, release)

选项:
    --revision <n>  回滚到指定版本 (默认: 上一个版本)
    --list          列出可用的版本历史
    --force         强制回滚，跳过确认
    --help          显示此帮助信息

示例:
    $0 sea preview --list           # 列出 SEA preview 的版本历史
    $0 sea preview                  # 回滚到上一个版本
    $0 eu release --revision 3      # 回滚到第3个版本
    $0 us release --force           # 强制回滚

EOF
}

# 验证参数
validate_args() {
    local valid_regions=("sea" "eu" "jp" "kr" "us")
    local valid_environments=("preview" "release")

    if [[ "$LIST_REVISIONS" == "false" ]]; then
        if [[ -z "$REGION" ]]; then
            log_error "缺少地区参数"
            show_help
            exit 1
        fi

        if [[ -z "$ENVIRONMENT" ]]; then
            log_error "缺少环境参数"
            show_help
            exit 1
        fi
    fi

    if [[ -n "$REGION" ]] && [[ ! " ${valid_regions[@]} " =~ " ${REGION} " ]]; then
        log_error "无效的地区: $REGION"
        log_info "支持的地区: ${valid_regions[*]}"
        exit 1
    fi

    if [[ -n "$ENVIRONMENT" ]] && [[ ! " ${valid_environments[@]} " =~ " ${ENVIRONMENT} " ]]; then
        log_error "无效的环境: $ENVIRONMENT"
        log_info "支持的环境: ${valid_environments[*]}"
        exit 1
    fi
}

# 检查依赖
check_dependencies() {
    if ! command -v kubectl &> /dev/null; then
        log_error "缺少依赖: kubectl"
        exit 1
    fi

    # 检查基本权限
    if ! kubectl auth can-i patch deployments &> /dev/null; then
        log_warning "当前用户可能没有回滚权限，请确认权限配置"
        # 尝试基本连接测试
        if ! kubectl get namespaces &> /dev/null; then
            log_error "无法连接到 Kubernetes 集群或权限不足"
            exit 1
        fi
    fi

    log_info "依赖检查通过"
}

# 获取部署信息
get_deployment_info() {
    local region="$1"
    local env="$2"

    local namespace="default"
    local deployment_name="$region-$env-action-server"

    # 检查部署是否存在
    if ! kubectl get deployment "$deployment_name" -n "$namespace" &> /dev/null; then
        log_error "部署不存在: $deployment_name in namespace $namespace"
        exit 1
    fi

    echo "$namespace:$deployment_name"
}

# 列出版本历史
list_revisions() {
    local region="$1"
    local env="$2"

    local deployment_info=$(get_deployment_info "$region" "$env")
    local namespace=$(echo "$deployment_info" | cut -d':' -f1)
    local deployment_name=$(echo "$deployment_info" | cut -d':' -f2)

    log_info "版本历史 - $region-$env:"
    echo
    kubectl rollout history deployment/"$deployment_name" -n "$namespace"
}

# 确认回滚
confirm_rollback() {
    if [[ "$FORCE" == "true" ]]; then
        return 0
    fi

    echo
    log_warning "即将回滚部署:"
    echo "  地区: $REGION"
    echo "  环境: $ENVIRONMENT"
    echo "  命名空间: default"
    if [[ -n "$REVISION" ]]; then
        echo "  目标版本: $REVISION"
    else
        echo "  目标版本: 上一个版本"
    fi
    echo

    read -p "确认继续回滚? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "回滚已取消"
        exit 0
    fi
}

# 执行回滚
perform_rollback() {
    local region="$1"
    local env="$2"
    local revision="$3"

    local deployment_info=$(get_deployment_info "$region" "$env")
    local namespace=$(echo "$deployment_info" | cut -d':' -f1)
    local deployment_name=$(echo "$deployment_info" | cut -d':' -f2)

    log_info "开始回滚 $region-$env"

    # 执行回滚
    if [[ -n "$revision" ]]; then
        kubectl rollout undo deployment/"$deployment_name" -n "$namespace" --to-revision="$revision"
    else
        kubectl rollout undo deployment/"$deployment_name" -n "$namespace"
    fi

    # 等待回滚完成
    log_info "等待回滚完成..."
    kubectl rollout status deployment/"$deployment_name" -n "$namespace" --timeout=300s

    # 检查 Pod 状态
    log_info "检查 Pod 状态..."
    kubectl get pods -n "$namespace" -l app=action-server

    log_success "回滚完成!"

    # 显示当前版本信息
    log_info "当前版本信息:"
    kubectl rollout history deployment/"$deployment_name" -n "$namespace" --revision=0
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --revision)
            REVISION="$2"
            shift 2
            ;;
        --list)
            LIST_REVISIONS=true
            shift
            ;;
        --force)
            FORCE=true
            shift
            ;;
        --help)
            show_help
            exit 0
            ;;
        -*)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
        *)
            if [[ -z "$REGION" ]]; then
                REGION="$1"
            elif [[ -z "$ENVIRONMENT" ]]; then
                ENVIRONMENT="$1"
            else
                log_error "过多的参数: $1"
                show_help
                exit 1
            fi
            shift
            ;;
    esac
done

# 主函数
main() {
    validate_args
    check_dependencies

    if [[ "$LIST_REVISIONS" == "true" ]]; then
        if [[ -z "$REGION" || -z "$ENVIRONMENT" ]]; then
            log_error "列出版本历史需要指定地区和环境"
            show_help
            exit 1
        fi
        list_revisions "$REGION" "$ENVIRONMENT"
        exit 0
    fi

    confirm_rollback
    perform_rollback "$REGION" "$ENVIRONMENT" "$REVISION"
}

main "$@"
