#!/bin/bash

# 生成各地区配置文件的脚本
# 使用方法: ./generate-configs.sh

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_DIR="$SCRIPT_DIR/../configs/action-server"

# 地区配置映射
declare -A REGIONS=(
    ["eu"]="eu"
    ["jp"]="jp" 
    ["kr"]="kr"
    ["us"]="us"
)

declare -A DB_USERS=(
    ["eu"]="nshm_eu"
    ["jp"]="nshm_jp"
    ["kr"]="nshm_kr"
    ["us"]="nshm_us"
)

declare -A DB_PASSWORDS=(
    ["eu"]="hdbcvydvc3"
    ["jp"]="sdcbygc12617dd"
    ["kr"]="ccwfbsdycgyu"
    ["us"]="ccwfbsdycgyu"
)

declare -A REDIS_HOSTS=(
    ["eu"]="r-gs5e6d8b28cac374.redis.singapore.rds.aliyuncs.com:6379"
    ["jp"]="r-gs5e6d8b28cac374.redis.singapore.rds.aliyuncs.com:6379"
    ["kr"]="r-gs5e6d8b28cac374.redis.singapore.rds.aliyuncs.com:6379"
    ["us"]="r-gs5e6d8b28cac374.redis.singapore.rds.aliyuncs.com:6379"
)

declare -A REDIS_PASSWORDS=(
    ["eu"]="61DWNqUc6tRM5GLa"
    ["jp"]="61DWNqUc6tRM5GLa"
    ["kr"]="61DWNqUc6tRM5GLa"
    ["us"]="61DWNqUc6tRM5GLa"
)

declare -A JWT_SECRETS=(
    ["eu"]="releaseForEU"
    ["jp"]="releaseForJp"
    ["kr"]="releaseForKr"
    ["us"]="releaseForUS"
)

# 生成配置文件函数
generate_config() {
    local region=$1
    local env=$2
    local config_file="$CONFIG_DIR/$region/${env}-config.toml"
    
    local db_name="l36_${region}"
    if [ "$env" = "preview" ]; then
        db_name="l36_${region}_preview"
    fi
    
    local log_level=3
    local test_env="false"
    local request_log="false"
    local dev_mode="false"
    local skip_audit="false"
    local fuxi_disable="false"
    
    if [ "$env" = "preview" ]; then
        log_level=5
        test_env="true"
        request_log="true"
        dev_mode="true"
        skip_audit="true"
        fuxi_disable="true"
    fi
    
    local domain_suffix=""
    if [ "$env" = "preview" ]; then
        domain_suffix="-preview"
    fi
    
    mkdir -p "$(dirname "$config_file")"
    
    cat > "$config_file" << EOF
# 应用配置
[App]
AppName = "action-station-server"
HttpListen = ":9992"
Maintainer = ["<EMAIL>"]

# Gin日志配置
[Gin.Log]
LogPath = "/srv/logs"
Schema = "com.netease.leihuo.ccc.l36md.model.tables.v1.L36MdLog"

# 应用日志配置
[Log]
LogPath = "/srv/logs"
GlobalFields.Schema = "com.netease.leihuo.ccc.l36md.model.tables.v1.L36MdLog"
YunyingLogPath = "/yunying"
Level = $log_level

# 数据库配置 - ${region^^} ${env^}
[Mysql]
Host = "rm-gs5p1rb1x9769740p.mysql.singapore.rds.aliyuncs.com"
Port = "3306"
DBName = "$db_name"
Username = "${DB_USERS[$region]}"
Password = "${DB_PASSWORDS[$region]}"
LogLevel = $((log_level - 1))

[SlaveMysql]
Host = "rm-gs5p1rb1x9769740p.mysql.singapore.rds.aliyuncs.com"
Port = "3306"
DBName = "$db_name"
Username = "${DB_USERS[$region]}"
Password = "${DB_PASSWORDS[$region]}"

[OnlineMysql]
Host = "rr-bp1fw79qp0x9uq94y.mysql.rds.aliyuncs.com"
Port = "3306"
DBName = "nshm"
Username = "nshm"
Password = "zRm3wVKNPhLGemgE"

# Redis配置 - ${region^^} ${env^}
[Redis]
Addr = "${REDIS_HOSTS[$region]}"
Password = "${REDIS_PASSWORDS[$region]}"
Prefix = "nshm:action-station-server:$region:$env:"

# POPO配置
[Popo]
Url = "https://lhpp-popo-server.apps-hp.danlu.netease.com/popo/popo/msg/send"
Salt = "lOMvNtTgjrT7WJrmXOknYA=="
Project = "nshm"
Biz = "action-station-server"
Env = "$env"

# 业务配置
[Biz]
CrossDomainOrigin = [
EOF

    if [ "$env" = "preview" ]; then
        cat >> "$config_file" << EOF
    "https://test.163.com*",
    "https://test.nie.163.com*",
    "https://test.yjwujian.cn*",
    "https://l36-ugc-workshop-test.apps-sl.danlu.netease.com*"
EOF
    else
        cat >> "$config_file" << EOF
    "https://api-$region.swordofjustice.com",
    "https://$region.swordofjustice.com"
EOF
    fi

    cat >> "$config_file" << EOF
]

[Test]
TestEnv = $test_env
RequestLog = $request_log

[Server]
Host = "action-server-$region$domain_suffix.swordofjustice.com"
BasePath = "/nshm/action-station"
Prefix = ""

[Pusher]
LogPath = "/srv/logs"
FileName = "pusher.log"
ReserveDays = 30
ReserveCount = 300
MaxSize = 1024
Schema = "com.netease.leihuo.ccc.base.model.tables.v1.CccLhppUdsp"

[Filepicker]
Project = "l36-action-$region$domain_suffix"
SecretKey = "06J8icHwptPMrsbpobVpGhFtnIKvGutl"
DevMode = $dev_mode
Region = "EaseBar"
Review = 1
Policy.MimeLimit = [
  "text/plain",
  "image/*",
  "application/json",
  "video/*",
]
Policy.FsizeLimit = [0, 209715200]
SecretToken = "MZBRtXlP5mZ1ZpTLy4DKTQxrurNUNAP1"
HTTPS = true
BasePath = "https://l36-action-$region$domain_suffix.fp.ps.netease.com/file/"
SkipAudit = $skip_audit

[Skey]
TestSkey = ""
TokenSecret = "UHwlPHpW4Ta%ePjJ&8^tXFPy0ikrZ6@5"
JwtSecret = "${JWT_SECRETS[$region]}"
Enable = true
EnableToken = true
Expire = 24

[Fuxi]
Host = "https://l36-ugc-expression-rec-11166-2335.apps-sl.danlu.netease.com/recom"
BackupCount = 1000
Disable = $fuxi_disable
Qps = 100

[ES]
Address = "https://l36-es-$region$domain_suffix-9200.apps-sl.danlu.netease.com"
User = "elastic"
Password = "nodePassword"
Suffix = "$region$domain_suffix"

[EnvSdk]
Disable = false
Host = "https://envsdkv4-online.apps-hp.danlu.netease.com"
Timeout = 500
Channel = "action_station"
Level = 1

[Work]
CollectLimit = 100
CreateLimit = 300
CreateExp = 10
EffectCreateLimit = 20
CommentExp = 2
CommentHot = 2
EffectCommentLimit = 10
LikeExp = 1
LikeHot = 1
EffectLikeLimit = 10
CollectExp = 1
CollectHot = 1
EffectCollectLimit = 10
CompletePlayExp = 2
CompletePlayHot = 2
EffectCompletePlayLimit = 10
UseHot = 10
UseExp = 10
DailyEffectUseLimit = 3
EffectUseLimit = 10
ShareHot = 3
ShareExp = 3
EffectShareLimit = 10
HotDecay = 0.9057

[FashionRecommend]
Host = "https://motion-to-cloth-prod-15928-7781.apps-sl.danlu.netease.com"

[UserLevel]
MaxLevel = 8
Exp = [
  { Level = 1, MinExp = 0, MaxExp = 40 },
  { Level = 2, MinExp = 40, MaxExp = 100 },
  { Level = 3, MinExp = 100, MaxExp = 200 },
  { Level = 4, MinExp = 200, MaxExp = 600 },
  { Level = 5, MinExp = 600, MaxExp = 2600 },
  { Level = 6, MinExp = 2600, MaxExp = 12600 },
  { Level = 7, MinExp = 12600, MaxExp = 42600 },
  { Level = 8, MinExp = 42600, MaxExp = 112600 },
  { Level = 9, MinExp = 112600, MaxExp = 312600 },
  { Level = 10, MinExp = 312600, MaxExp = 999999999 }
]
EOF

    echo "Generated config: $config_file"
}

# 生成所有地区的配置文件
for region in "${!REGIONS[@]}"; do
    echo "Generating configs for region: $region"
    generate_config "$region" "preview"
    generate_config "$region" "release"
done

echo "All configuration files generated successfully!"
