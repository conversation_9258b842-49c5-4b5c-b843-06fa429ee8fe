#!/bin/bash

# 生成各地区各环境的 Kustomize 覆盖配置
# 使用方法: ./generate-overlays.sh

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
OVERLAYS_DIR="$SCRIPT_DIR/../overlays"

# 地区列表
REGIONS=("sea" "eu" "jp" "kr" "us")
ENVIRONMENTS=("preview" "release")

# 生成 kustomization.yaml
generate_kustomization() {
    local region=$1
    local env=$2
    local overlay_dir="$OVERLAYS_DIR/$region/$env"

    mkdir -p "$overlay_dir"

    local image_tag="$env"
    if [ "$env" = "release" ]; then
        image_tag="latest"
    fi

    local replicas=2
    if [ "$env" = "release" ]; then
        replicas=3
    fi

    cat > "$overlay_dir/kustomization.yaml" << EOF
apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: default

resources:
  - ../../../base/action-server
  - ingress.yaml

patchesStrategicMerge:
  - deployment-patch.yaml

configMapGenerator:
  - name: action-server-config
    files:
      - config.toml

images:
  - name: l36na-reg.leihuo.netease.com/ccc/nshm-action-station-server
    newTag: $image_tag

commonLabels:
  region: $region
  environment: $env
  app: action-server

namePrefix: $region-$env-

replicas:
  - name: action-server
    count: $replicas
EOF
}

# 生成 namespace.yaml (已禁用，使用默认命名空间)
generate_namespace() {
    local region=$1
    local env=$2
    local overlay_dir="$OVERLAYS_DIR/$region/$env"

    # 不再生成 namespace.yaml，使用默认命名空间
    return 0
}

# 生成 deployment-patch.yaml
generate_deployment_patch() {
    local region=$1
    local env=$2
    local overlay_dir="$OVERLAYS_DIR/$region/$env"

    local memory_request="256Mi"
    local memory_limit="512Mi"
    local cpu_request="100m"
    local cpu_limit="300m"

    if [ "$env" = "release" ]; then
        memory_request="512Mi"
        memory_limit="1Gi"
        cpu_request="200m"
        cpu_limit="500m"
    fi

    cat > "$overlay_dir/deployment-patch.yaml" << EOF
apiVersion: apps/v1
kind: Deployment
metadata:
  name: action-server
spec:
  template:
    spec:
      containers:
        - name: action-server
          env:
            - name: ENVIRONMENT
              value: "$env"
            - name: REGION
              value: "$region"
          resources:
            requests:
              memory: "$memory_request"
              cpu: "$cpu_request"
            limits:
              memory: "$memory_limit"
              cpu: "$cpu_limit"
EOF
}

# 生成 ingress.yaml
generate_ingress() {
    local region=$1
    local env=$2
    local overlay_dir="$OVERLAYS_DIR/$region/$env"

    local domain_suffix=""
    if [ "$env" = "preview" ]; then
        domain_suffix="-preview"
    fi

    cat > "$overlay_dir/ingress.yaml" << EOF
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: action-server-ingress
  labels:
    app: action-server
    region: $region
    environment: $env
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
spec:
  ingressClassName: nginx
  tls:
    - hosts:
        - action-server-$region$domain_suffix.swordofjustice.com
      secretName: action-server-$region-$env-tls
  rules:
    - host: action-server-$region$domain_suffix.swordofjustice.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: $region-$env-action-server
                port:
                  number: 80
EOF
}

# 复制配置文件到覆盖目录
copy_config_files() {
    local region=$1
    local env=$2
    local overlay_dir="$OVERLAYS_DIR/$region/$env"
    local config_source="$SCRIPT_DIR/../configs/action-server/$region/${env}-config.toml"

    if [[ -f "$config_source" ]]; then
        cp "$config_source" "$overlay_dir/config.toml"
        echo "Copied config file to $overlay_dir/config.toml"
    else
        echo "Warning: Config file not found: $config_source"
    fi
}

# 生成所有地区和环境的配置
for region in "${REGIONS[@]}"; do
    for env in "${ENVIRONMENTS[@]}"; do
        echo "Generating overlay for $region-$env"
        generate_kustomization "$region" "$env"
        generate_namespace "$region" "$env"
        generate_deployment_patch "$region" "$env"
        generate_ingress "$region" "$env"
        copy_config_files "$region" "$env"
    done
done

echo "All overlay configurations generated successfully!"
