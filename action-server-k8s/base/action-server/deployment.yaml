apiVersion: apps/v1
kind: Deployment
metadata:
  name: action-server
  labels:
    app: action-server
    component: backend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: action-server
  template:
    metadata:
      labels:
        app: action-server
        component: backend
    spec:
      volumes:
        - name: config-volume
          configMap:
            name: action-server-config
            items:
              - key: config.toml
                path: config.toml
        - name: logs-volume
          hostPath:
            path: /srv/logs
            type: DirectoryOrCreate
      containers:
        - name: action-server
          image: l36na-reg.leihuo.netease.com/ccc/nshm-action-station-server:latest
          ports:
            - name: http
              containerPort: 9992
              protocol: TCP
          volumeMounts:
            - name: config-volume
              mountPath: /app/config.toml
              subPath: config.toml
            - name: logs-volume
              mountPath: /srv/logs
          env:
            - name: CONFIG_PATH
              value: "/app/config.toml"
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
          resources:
            requests:
              memory: "512Mi"
              cpu: "200m"
            limits:
              memory: "1Gi"
              cpu: "500m"
          livenessProbe:
            httpGet:
              path: /health
              port: 9992
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /ready
              port: 9992
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 3
            failureThreshold: 3
          startupProbe:
            httpGet:
              path: /health
              port: 9992
            initialDelaySeconds: 10
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 30
