apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: default

resources:
  - ../../../base/action-server
  - ingress.yaml

patchesStrategicMerge:
  - deployment-patch.yaml

configMapGenerator:
  - name: action-server-config
    files:
      - config.toml

images:
  - name: l36na-reg.leihuo.netease.com/ccc/nshm-action-station-server
    newTag: preview

commonLabels:
  region: kr
  environment: preview
  app: action-server

namePrefix: kr-preview-

replicas:
  - name: action-server
    count: 2
