apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: action-server-ingress
  labels:
    app: action-server
    region: eu
    environment: release
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
spec:
  ingressClassName: nginx
  tls:
    - hosts:
        - action-server-eu.swordofjustice.com
      secretName: action-server-eu-release-tls
  rules:
    - host: action-server-eu.swordofjustice.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: eu-release-action-server
                port:
                  number: 80
