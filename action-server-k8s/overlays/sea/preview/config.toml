# 应用配置
[App]
AppName = "action-station-server"
HttpListen = ":9992"
Maintainer = ["<EMAIL>"]

# Gin日志配置
[Gin.Log]
LogPath = "/srv/logs"
Schema = "com.netease.leihuo.ccc.l36md.model.tables.v1.L36MdLog"

# 应用日志配置
[Log]
LogPath = "/srv/logs"
GlobalFields.Schema = "com.netease.leihuo.ccc.l36md.model.tables.v1.L36MdLog"
YunyingLogPath = "/yunying"
Level = 5

# 数据库配置 - SEA Preview
[Mysql]
Host = "rm-gs5p1rb1x9769740p.mysql.singapore.rds.aliyuncs.com"
Port = "3306"
DBName = "l36_sea_preview"
Username = "nshm_sea"
Password = "acsdb67fc157t3fvcd"
LogLevel = 4

[SlaveMysql]
Host = "rm-gs5p1rb1x9769740p.mysql.singapore.rds.aliyuncs.com"
Port = "3306"
DBName = "l36_sea_preview"
Username = "nshm_sea"
Password = "acsdb67fc157t3fvcd"

[OnlineMysql]
Host = "rr-bp1fw79qp0x9uq94y.mysql.rds.aliyuncs.com"
Port = "3306"
DBName = "nshm"
Username = "nshm"
Password = "zRm3wVKNPhLGemgE"

# Redis配置 - SEA Preview
[Redis]
Addr = "r-gs5p1rb1x9769740p.redis.singapore.rds.aliyuncs.com:6379"
Password = "acsdb67fc157t3fvcd"
Prefix = "nshm:action-station-server:sea:preview:"

# POPO配置
[Popo]
Url = "https://lhpp-popo-server.apps-hp.danlu.netease.com/popo/popo/msg/send"
Salt = "lOMvNtTgjrT7WJrmXOknYA=="
Project = "nshm"
Biz = "action-station-server"
Env = "preview"

# 业务配置
[Biz]
CrossDomainOrigin = [
    "https://test.163.com*",
    "https://test.nie.163.com*",
    "https://test.yjwujian.cn*",
    "https://l36-ugc-workshop-test.apps-sl.danlu.netease.com*"
]

[Test]
TestEnv = true
RequestLog = true

[Server]
Host = "action-server-sea-preview.swordofjustice.com"
BasePath = "/nshm/action-station"
Prefix = ""

[Pusher]
LogPath = "/srv/logs"
FileName = "pusher.log"
ReserveDays = 30
ReserveCount = 300
MaxSize = 1024
Schema = "com.netease.leihuo.ccc.base.model.tables.v1.CccLhppUdsp"

[Filepicker]
Project = "l36-action-sea-preview"
SecretKey = "06J8icHwptPMrsbpobVpGhFtnIKvGutl"
DevMode = true
Region = "EaseBar"
Review = 1
Policy.MimeLimit = [
  "text/plain",
  "image/*",
  "application/json",
  "video/*",
]
Policy.FsizeLimit = [0, 209715200]
SecretToken = "MZBRtXlP5mZ1ZpTLy4DKTQxrurNUNAP1"
HTTPS = true
BasePath = "https://l36-action-sea-preview.fp.ps.netease.com/file/"
SkipAudit = true

[Skey]
TestSkey = "skey_in_test"
TokenSecret = "UHwlPHpW4Ta%ePjJ&8^tXFPy0ikrZ6@5"
JwtSecret = "R8LO6hpJa$BGaVy*I#J#mMJB!iH%luta"
Enable = true
EnableToken = true
Expire = 24

[Fuxi]
Host = "http://ccc-gw-mock.apps-sl.danlu.netease.com/recom"
BackupCount = 1000
Disable = true
Qps = 100

[ES]
Address = "https://l36-es-sea-preview-9200.apps-sl.danlu.netease.com"
User = "elastic"
Password = "nodePassword"
Suffix = "sea_preview"

[EnvSdk]
Disable = false
Host = "https://envsdkv4-online.apps-hp.danlu.netease.com"
Timeout = 500
Channel = "action_station"
Level = 1

[Work]
CollectLimit = 100
CreateLimit = 300
CreateExp = 10
EffectCreateLimit = 20
CommentExp = 2
CommentHot = 2
EffectCommentLimit = 10
LikeExp = 1
LikeHot = 1
EffectLikeLimit = 10
CollectExp = 1
CollectHot = 1
EffectCollectLimit = 10
CompletePlayExp = 2
CompletePlayHot = 2
EffectCompletePlayLimit = 10
UseHot = 10
UseExp = 10
DailyEffectUseLimit = 3
EffectUseLimit = 10
ShareHot = 3
ShareExp = 3
EffectShareLimit = 10
HotDecay = 0.9057

[FashionRecommend]
Host = "https://motion-to-cloth-test-15928-7781.apps-sl.danlu.netease.com"

[UserLevel]
MaxLevel = 8
Exp = [
  { Level = 1, MinExp = 0, MaxExp = 40 },
  { Level = 2, MinExp = 40, MaxExp = 100 },
  { Level = 3, MinExp = 100, MaxExp = 200 },
  { Level = 4, MinExp = 200, MaxExp = 600 },
  { Level = 5, MinExp = 600, MaxExp = 2600 },
  { Level = 6, MinExp = 2600, MaxExp = 12600 },
  { Level = 7, MinExp = 12600, MaxExp = 42600 },
  { Level = 8, MinExp = 42600, MaxExp = 112600 },
  { Level = 9, MinExp = 112600, MaxExp = 312600 },
  { Level = 10, MinExp = 312600, MaxExp = 999999999 }
]
