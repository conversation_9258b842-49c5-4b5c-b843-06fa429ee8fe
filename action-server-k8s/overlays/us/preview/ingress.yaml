apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: action-server-ingress
  labels:
    app: action-server
    region: us
    environment: preview
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
spec:
  ingressClassName: nginx
  tls:
    - hosts:
        - action-server-us-preview.swordofjustice.com
      secretName: action-server-us-preview-tls
  rules:
    - host: action-server-us-preview.swordofjustice.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: us-preview-action-server
                port:
                  number: 80
