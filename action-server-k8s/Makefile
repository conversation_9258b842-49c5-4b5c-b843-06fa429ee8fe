# Action Server Kubernetes 部署 Makefile

.PHONY: help validate deploy status rollback clean generate-configs generate-overlays

# 默认目标
.DEFAULT_GOAL := help

# 颜色定义
BLUE := \033[36m
GREEN := \033[32m
YELLOW := \033[33m
RED := \033[31m
NC := \033[0m

# 变量
REGION ?= sea
ENV ?= preview
IMAGE_TAG ?= latest

help: ## 显示帮助信息
	@echo "$(BLUE)Action Server Kubernetes 部署工具$(NC)"
	@echo ""
	@echo "$(YELLOW)使用方法:$(NC)"
	@echo "  make <target> [REGION=<region>] [ENV=<env>] [IMAGE_TAG=<tag>]"
	@echo ""
	@echo "$(YELLOW)可用目标:$(NC)"
	@awk 'BEGIN {FS = ":.*##"} /^[a-zA-Z_-]+:.*##/ { printf "  $(GREEN)%-20s$(NC) %s\n", $$1, $$2 }' $(MAKEFILE_LIST)
	@echo ""
	@echo "$(YELLOW)示例:$(NC)"
	@echo "  make validate                    # 验证所有配置"
	@echo "  make deploy REGION=sea ENV=preview  # 部署到 SEA preview"
	@echo "  make status REGION=eu            # 查看 EU 地区状态"
	@echo "  make rollback REGION=us ENV=release  # 回滚 US release"
	@echo ""
	@echo "$(YELLOW)支持的地区:$(NC) sea, eu, jp, kr, us"
	@echo "$(YELLOW)支持的环境:$(NC) preview, release"

validate: ## 验证配置文件
	@echo "$(BLUE)验证配置文件...$(NC)"
	@./scripts/validate.sh

validate-region: ## 验证指定地区配置
	@echo "$(BLUE)验证 $(REGION) 地区配置...$(NC)"
	@./scripts/validate.sh $(REGION)

validate-env: ## 验证指定地区和环境配置
	@echo "$(BLUE)验证 $(REGION)-$(ENV) 配置...$(NC)"
	@./scripts/validate.sh $(REGION) $(ENV)

deploy: ## 部署到指定环境
	@echo "$(BLUE)部署到 $(REGION)-$(ENV)...$(NC)"
	@./scripts/deploy.sh $(REGION) $(ENV) --image-tag $(IMAGE_TAG)

deploy-dry-run: ## 预览部署（不实际执行）
	@echo "$(BLUE)预览部署到 $(REGION)-$(ENV)...$(NC)"
	@./scripts/deploy.sh $(REGION) $(ENV) --dry-run --image-tag $(IMAGE_TAG)

deploy-force: ## 强制部署（跳过确认）
	@echo "$(BLUE)强制部署到 $(REGION)-$(ENV)...$(NC)"
	@./scripts/deploy.sh $(REGION) $(ENV) --force --image-tag $(IMAGE_TAG)

status: ## 查看所有部署状态
	@echo "$(BLUE)查看部署状态...$(NC)"
	@./scripts/status.sh

status-region: ## 查看指定地区状态
	@echo "$(BLUE)查看 $(REGION) 地区状态...$(NC)"
	@./scripts/status.sh $(REGION)

status-env: ## 查看指定环境状态
	@echo "$(BLUE)查看 $(REGION)-$(ENV) 状态...$(NC)"
	@./scripts/status.sh $(REGION) $(ENV)

rollback: ## 回滚到上一个版本
	@echo "$(BLUE)回滚 $(REGION)-$(ENV)...$(NC)"
	@./scripts/rollback.sh $(REGION) $(ENV)

rollback-list: ## 列出版本历史
	@echo "$(BLUE)列出 $(REGION)-$(ENV) 版本历史...$(NC)"
	@./scripts/rollback.sh $(REGION) $(ENV) --list

rollback-force: ## 强制回滚
	@echo "$(BLUE)强制回滚 $(REGION)-$(ENV)...$(NC)"
	@./scripts/rollback.sh $(REGION) $(ENV) --force

generate-configs: ## 重新生成所有配置文件
	@echo "$(BLUE)生成配置文件...$(NC)"
	@./scripts/generate-configs.sh
	@echo "$(GREEN)配置文件生成完成$(NC)"

generate-overlays: ## 重新生成所有覆盖配置
	@echo "$(BLUE)生成覆盖配置...$(NC)"
	@./scripts/generate-overlays.sh
	@echo "$(GREEN)覆盖配置生成完成$(NC)"

regenerate: generate-configs generate-overlays ## 重新生成所有配置

clean: ## 清理临时文件
	@echo "$(BLUE)清理临时文件...$(NC)"
	@find . -name "*.tmp" -delete
	@find . -name ".DS_Store" -delete
	@echo "$(GREEN)清理完成$(NC)"

# 批量操作
deploy-all-preview: ## 部署所有地区的 preview 环境
	@echo "$(BLUE)部署所有地区的 preview 环境...$(NC)"
	@for region in sea eu jp kr us; do \
		echo "$(YELLOW)部署 $$region-preview...$(NC)"; \
		./scripts/deploy.sh $$region preview --force --image-tag $(IMAGE_TAG) || exit 1; \
	done
	@echo "$(GREEN)所有 preview 环境部署完成$(NC)"

deploy-all-release: ## 部署所有地区的 release 环境
	@echo "$(BLUE)部署所有地区的 release 环境...$(NC)"
	@for region in sea eu jp kr us; do \
		echo "$(YELLOW)部署 $$region-release...$(NC)"; \
		./scripts/deploy.sh $$region release --force --image-tag $(IMAGE_TAG) || exit 1; \
	done
	@echo "$(GREEN)所有 release 环境部署完成$(NC)"

# 开发工具
logs: ## 查看应用日志
	@echo "$(BLUE)查看 $(REGION)-$(ENV) 日志...$(NC)"
	@kubectl logs -n default -l app=action-server,region=$(REGION),environment=$(ENV) --tail=100 -f

exec: ## 进入 Pod 调试
	@echo "$(BLUE)进入 $(REGION)-$(ENV) Pod...$(NC)"
	@kubectl exec -it -n default deployment/$(REGION)-$(ENV)-action-server -- /bin/sh

port-forward: ## 端口转发到本地
	@echo "$(BLUE)端口转发 $(REGION)-$(ENV) 到本地 8080...$(NC)"
	@kubectl port-forward -n default deployment/$(REGION)-$(ENV)-action-server 8080:9992

# 检查工具
check-deps: ## 检查依赖工具
	@echo "$(BLUE)检查依赖工具...$(NC)"
	@command -v kubectl >/dev/null 2>&1 || { echo "$(RED)kubectl 未安装$(NC)"; exit 1; }
	@command -v kustomize >/dev/null 2>&1 || echo "$(YELLOW)kustomize 未安装，将使用 kubectl 内置版本$(NC)"
	@kubectl get namespaces >/dev/null 2>&1 || { echo "$(RED)无法连接到 Kubernetes 集群或权限不足$(NC)"; exit 1; }
	@echo "$(GREEN)依赖检查通过$(NC)"

check-permissions: ## 检查 Kubernetes 权限
	@echo "$(BLUE)检查 Kubernetes 权限...$(NC)"
	@./scripts/check-permissions.sh

check-config: ## 检查配置文件完整性
	@echo "$(BLUE)检查配置文件完整性...$(NC)"
	@for region in sea eu jp kr us; do \
		for env in preview release; do \
			if [ ! -f "configs/action-server/$$region/$$env-config.toml" ]; then \
				echo "$(RED)缺少配置文件: configs/action-server/$$region/$$env-config.toml$(NC)"; \
				exit 1; \
			fi; \
		done; \
	done
	@echo "$(GREEN)配置文件检查通过$(NC)"

# 信息显示
info: ## 显示当前配置信息
	@echo "$(BLUE)当前配置信息:$(NC)"
	@echo "  地区: $(YELLOW)$(REGION)$(NC)"
	@echo "  环境: $(YELLOW)$(ENV)$(NC)"
	@echo "  镜像标签: $(YELLOW)$(IMAGE_TAG)$(NC)"
	@echo "  命名空间: $(YELLOW)default$(NC)"
	@echo "  部署名称: $(YELLOW)$(REGION)-$(ENV)-action-server$(NC)"

# 快捷方式
sea-preview: ## 部署到 SEA preview
	@$(MAKE) deploy REGION=sea ENV=preview

sea-release: ## 部署到 SEA release
	@$(MAKE) deploy REGION=sea ENV=release

eu-preview: ## 部署到 EU preview
	@$(MAKE) deploy REGION=eu ENV=preview

eu-release: ## 部署到 EU release
	@$(MAKE) deploy REGION=eu ENV=release
