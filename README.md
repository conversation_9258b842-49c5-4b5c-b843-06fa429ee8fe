# NSHM na部署

逆水寒手游服务的 Kubernetes 部署配置。

## 概述

本仓库包含 NSHM（逆水寒手游）游戏平台在多个地区（美国、欧洲、日本、韩国、东南亚）的 Kubernetes 部署清单。部署包括客户端 API、服务器组件、入口配置和支撑基础设施。

## 项目结构

```
k8s/
├── db/                    # 数据库配置（MySQL）
├── filebeat/             # 日志收集和转发（Filebeat DaemonSets）
├── ingress/              # 全局入口配置
├── sshd/                 # SSH 守护进程服务
├── tls/                  # TLS 证书
├── inner_test/           # 内部测试环境
└── [region]/             # 区域特定部署
    ├── ingress/          # 区域特定入口规则
    ├── release/          # 生产部署
    └── test/             # 测试部署
```

### 区域部署

- **us/** - 美国
- **eu/** - 欧洲
- **jp/** - 日本
- **kr/** - 韩国
- **sea/** - 东南亚

每个区域包含：
- **客户端部署** - 前端 API 服务
- **服务器部署** - 后端游戏逻辑
- **消费者服务** - 消息队列消费者
- **定时任务服务** - 计划任务
- **入口规则** - 流量路由和负载均衡

## 服务

### 核心服务
- **l36-md-api** - NSHM 朋友圈主服务
- **l36-fashion-api** - 时装站系统 API
- **l36-garden-api** - 庄园站游戏 API

### 支撑服务
- **MySQL** - 主数据库
- **Filebeat** - 日志收集并转发到 Kafka
- **SSHD** - 调试用 SSH 访问
- **Ingress** - 基于 NGINX 的负载均衡和 SSL 终止

## 快速开始

### 前置条件
- 配置了 NGINX Ingress Controller 的 Kubernetes 集群
- 已配置集群访问权限的 kubectl
- Docker 镜像仓库访问权限（l36na-reg.leihuo.netease.com）

### 部署到区域

1. **选择目标区域和环境：**
   ```bash
   # 生产环境
   kubectl apply -f k8s/[region]/release/

   # 测试环境
   kubectl apply -f k8s/[region]/test/
   ```

2. **应用入口规则：**
   ```bash
   kubectl apply -f k8s/[region]/ingress/release/
   ```

3. **验证部署：**
   ```bash
   kubectl get pods -n [namespace]
   kubectl get ingress -n [namespace]
   ```

## 配置

### ConfigMaps
每个部署使用 ConfigMaps 进行配置管理：
- 应用配置（`config.js`）
- PM2 进程管理器配置（`pm2.json`）

### 环境特定设置
- **Release**：生产配置，包含资源限制
- **Test**：开发配置，包含调试设置

## 监控和日志

### 健康检查
所有服务包含：
- **就绪探针**：`/l36/healthCheck`
- **存活探针**：`/l36/healthCheck`
- **启动探针**：初始健康验证

### 日志收集
Filebeat DaemonSet 自动收集以下日志：
- 应用日志：`/srv/data/logs/app/`
- 业务分析：`/srv/data/logs/bury/`

日志转发到 Kafka 主题进行集中处理。

## 安全

### TLS 配置
- SSL 证书存储在 `k8s/tls/`
- 通过入口自动 HTTPS 重定向
- 安全头和访问控制

### 访问控制
- 生产环境屏蔽 Swagger/API 文档
- 环境间命名空间隔离
- 镜像仓库访问的密钥管理

## 扩展

### 资源限制
每个服务的默认资源分配：
- **内存**：500Mi 请求/限制
- **CPU**：100m 请求/限制

### 水平扩展
在部署清单中调整副本数：
```yaml
spec:
  replicas: 3  # 根据需要扩展
```

## 故障排除

### 常见问题
1. **Pod 未启动**：检查资源限制和节点容量
2. **服务不可达**：验证服务和入口配置
3. **镜像拉取错误**：确认镜像仓库凭据

### 调试命令
```bash
# 检查 Pod 状态
kubectl describe pod [pod-name] -n [namespace]

# 查看日志
kubectl logs [pod-name] -n [namespace]

# 访问 Pod Shell
kubectl exec -it [pod-name] -n [namespace] -- /bin/bash
```

## 开发

### 本地测试
使用 `test/` 配置进行开发环境，包含：
- 降低的资源需求
- 启用调试日志
- 开发域名

### CI/CD 集成
部署清单支持通过镜像标签更新进行自动部署。

## 支持

如遇部署问题或疑问：
1. 检查服务健康端点
2. 通过 kubectl 查看应用日志
3. 验证网络连接和 DNS 解析

## 贡献

添加新区域或服务时：
1. 遵循现有命名约定
2. 包含测试和发布配置
3. 添加适当的入口规则
4. 更新此文档